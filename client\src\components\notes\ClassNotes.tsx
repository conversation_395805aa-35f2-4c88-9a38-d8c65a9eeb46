
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { PlusCircle, Search, Filter, Calendar, SortAsc, SortDesc } from "lucide-react";
import NoteDetailDialog from "./NoteDetailDialog";
import CreateNoteDialog from "./CreateNoteDialog";
import { getCurrentUser, hasPermission } from "@/lib/auth";
import { canCreateNotes } from "@/lib/noteUtils";

import { fetchClassNotes } from "@/services/notesService";
import { apiClient } from "@/lib/api";

interface ClassNotesProps {
  classId: string;
}

const ClassNotes = ({ classId }: ClassNotesProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedNoteId, setSelectedNoteId] = useState<string | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  
  const user = getCurrentUser();
  
  // Fetch class information
  const { data: classData, isLoading: isClassLoading } = useQuery({
    queryKey: ["class", classId],
    queryFn: async () => {
      const response = await apiClient.get(`/classes/${classId}`);
      return response.data.success ? response.data.data : null;
    },
  });

  // Fetch class notes
  const { data: notesData, isLoading: isNotesLoading, error, refetch } = useQuery({
    queryKey: ["classNotes", classId, searchQuery],
    queryFn: () => fetchClassNotes(classId, {
      search: searchQuery || undefined,
      limit: 50,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    }),
  });

  const classInfo = classData;
  const notes = notesData?.notes || [];
  const isLoading = isClassLoading || isNotesLoading;

  // Since we're filtering on the server side, no need for client-side filtering
  const filteredNotes = notes;
  
  const handleNoteCreated = () => {
    setIsCreateDialogOpen(false);
    toast.success("Note created successfully");
    refetch();
  };
  
  // Find the currently selected note
  const selectedNote = selectedNoteId 
    ? notes.find(note => note.id === selectedNoteId) 
    : null;
  
  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-red-500">
            Error loading class notes
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              {isLoading ? (
                <>
                  <Skeleton className="h-7 w-48 mb-2" />
                  <Skeleton className="h-5 w-32" />
                </>
              ) : (
                <>
                  <CardTitle>{classInfo?.name || `Class ${classId}`}</CardTitle>
                  <CardDescription>
                    {classInfo?.level} • Teacher: {classInfo?.teacher}
                  </CardDescription>
                </>
              )}
            </div>
            {user && canCreateNotes(user.role) && (
              <Button onClick={() => setIsCreateDialogOpen(true)}>
                <PlusCircle className="mr-2 h-4 w-4" />
                Add Note
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between mb-4">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search notes..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[300px]">Title</TableHead>
                  <TableHead>Created By</TableHead>
                  <TableHead>
                    <div className="flex items-center">
                      Date
                      <SortAsc className="ml-1 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead>Type</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array(3).fill(0).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-5 w-40" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-20" /></TableCell>
                    </TableRow>
                  ))
                ) : error ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-8 text-red-500">
                      Error loading notes. Please try again.
                    </TableCell>
                  </TableRow>
                ) : filteredNotes.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={4} className="text-center py-8 text-muted-foreground">
                      {searchQuery ? `No notes found matching "${searchQuery}"` : "No notes found for this class. Create the first note to get started."}
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredNotes.map((note) => (
                    <TableRow 
                      key={note.id} 
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => setSelectedNoteId(note.id)}
                    >
                      <TableCell className="font-medium">{note.title}</TableCell>
                      <TableCell>{note.createdBy.name}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                          {new Date(note.createdAt).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          note.type === 'announcement' 
                            ? 'bg-blue-100 text-blue-800' 
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {note.type.charAt(0).toUpperCase() + note.type.slice(1)}
                        </span>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
      
      {selectedNote && (
        <NoteDetailDialog
          note={selectedNote}
          open={!!selectedNoteId}
          onOpenChange={(open) => {
            if (!open) setSelectedNoteId(null);
          }}
          onNoteUpdated={() => refetch()}
        />
      )}
      
      <CreateNoteDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onNoteCreated={handleNoteCreated}
        initialClassId={classId}
      />
    </div>
  );
};

export default ClassNotes;
