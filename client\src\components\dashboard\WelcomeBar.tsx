
import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { UserRole } from "@/types";
import { Calendar } from "lucide-react";
import { getClassStatistics, getStudentStatistics } from "@/services/dashboardService";

interface WelcomeBarProps {}

const WelcomeBar = ({}: WelcomeBarProps) => {
  const { user } = useAuth();
  const [stats, setStats] = useState<{totalStudents: number, activeClasses: number}>({
    totalStudents: 0,
    activeClasses: 0
  });
  const [isLoading, setIsLoading] = useState(true);

  const currentDate = new Date();
  const formattedDate = new Intl.DateTimeFormat('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(currentDate);

  // Get stats on component mount
  useEffect(() => {
    const fetchStats = async () => {
      if (!user) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);

        // Only fetch stats if we have a valid JWT token
        const token = localStorage.getItem('jwt_token');
        if (!token) {
          console.warn('No JWT token found, skipping stats fetch');
          setIsLoading(false);
          return;
        }

        // Fetch dashboard stats with error handling
        try {
          const [classStats, studentStats] = await Promise.all([
            getClassStatistics().catch(() => ({ activeClasses: 0 })),
            getStudentStatistics().catch(() => ({ totalStudents: 0 }))
          ]);

          setStats({
            totalStudents: studentStats.totalStudents || 0,
            activeClasses: classStats.activeClasses || 0
          });
        } catch (statsError) {
          console.warn('Error fetching stats, using defaults:', statsError);
          // Keep default values (0, 0) on error
        }
      } catch (error) {
        console.error('Error fetching stats:', error);
        // Keep default values on error
      } finally {
        setIsLoading(false);
      }
    };

    fetchStats();
  }, [user]);

  // Get greeting based on time of day
  const getGreeting = () => {
    const hour = currentDate.getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  // Get role-specific welcome message
  const getWelcomeMessage = (role: UserRole) => {
    if (isLoading) {
      return "Loading dashboard data...";
    }

    switch (role) {
      case 'superAdmin':
        return `You have ${stats.totalStudents} students and ${stats.activeClasses} active classes`;
      case 'manager':
        return `Manage ${stats.activeClasses} active classes with ${stats.totalStudents} students`;
      case 'secretary':
        return `Assist with ${stats.totalStudents} students across ${stats.activeClasses} classes`;
      case 'teacher':
        return `You're teaching classes with a total of ${stats.totalStudents} students`;
      case 'student':
        return `You're enrolled in classes`;
      default:
        return `Welcome to Vertex Education`;
    }
  };

  if (!user) return null;

  return (
    <div className="bg-blue-50 border border-blue-100 rounded-xl p-6 mb-6 animate-slide-down">
      <div className="flex flex-col md:flex-row md:items-center justify-between">
        <div className="space-y-2">
          <h1 className="text-2xl font-semibold text-blue-900">
            {getGreeting()}, {user.name}
          </h1>
          <p className="text-blue-700 text-sm">
            {getWelcomeMessage(user.role)}
          </p>
        </div>
        <div className="flex items-center mt-3 md:mt-0 space-x-1.5 text-blue-700 text-sm">
          <Calendar size={16} />
          <span>{formattedDate}</span>
        </div>
      </div>
    </div>
  );
};

export default WelcomeBar;
