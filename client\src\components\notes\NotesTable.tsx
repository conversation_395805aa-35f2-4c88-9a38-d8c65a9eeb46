
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import { Search, Filter, Calendar, MoreHorizontal, FileText, User, BookOpen } from "lucide-react";
import NoteDetailDialog from "./NoteDetailDialog";
import { getCurrentUser } from "@/lib/auth";

import { fetchNotes } from "@/services/notesService";


interface NotesTableProps {
  onStudentSelect: (studentId: string) => void;
  onClassSelect: (classId: string) => void;
}

const NotesTable = ({ onStudentSelect, onClassSelect }: NotesTableProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedNoteId, setSelectedNoteId] = useState<string | null>(null);
  
  const currentUser = getCurrentUser();
  
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ["allNotes", searchQuery],
    queryFn: () => fetchNotes({
      search: searchQuery || undefined,
      limit: 50,
      sortBy: 'createdAt',
      sortOrder: 'desc'
    }),
  });

  const notes = data?.notes || [];
  
  // Since we're now filtering on the server side, we don't need client-side filtering
  const filteredNotes = notes;
  
  // Find the currently selected note
  const selectedNote = selectedNoteId 
    ? notes.find(note => note.id === selectedNoteId) 
    : null;
  
  const handleNoteClick = (noteId: string) => {
    setSelectedNoteId(noteId);
  };
  
  if (error) {
    return (
      <Card className="p-6">
        <div className="text-center text-red-500">
          Error loading notes
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="relative w-full max-w-sm">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search all notes..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button variant="outline" size="icon">
          <Filter className="h-4 w-4" />
        </Button>
      </div>
      
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[250px]">Title</TableHead>
              <TableHead>Created By</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Student</TableHead>
              <TableHead>Class</TableHead>
              <TableHead className="w-[50px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              Array(5).fill(0).map((_, index) => (
                <TableRow key={index}>
                  <TableCell><Skeleton className="h-5 w-40" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-32" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-28" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-28" /></TableCell>
                  <TableCell><Skeleton className="h-5 w-8" /></TableCell>
                </TableRow>
              ))
            ) : error ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8 text-red-500">
                  Error loading notes. Please try again.
                </TableCell>
              </TableRow>
            ) : filteredNotes.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                  {searchQuery ? `No notes found matching "${searchQuery}"` : "No notes found. Create your first note to get started."}
                </TableCell>
              </TableRow>
            ) : (
              filteredNotes.map((note) => (
                <TableRow key={note.id} className="hover:bg-muted/50">
                  <TableCell 
                    className="font-medium cursor-pointer"
                    onClick={() => handleNoteClick(note.id)}
                  >
                    {note.title}
                  </TableCell>
                  <TableCell>{note.createdBy.name}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                      {new Date(note.createdAt).toLocaleDateString()}
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      note.type === 'academic' 
                        ? 'bg-green-100 text-green-800' 
                        : note.type === 'administrative'
                        ? 'bg-purple-100 text-purple-800'
                        : note.type === 'announcement'
                        ? 'bg-blue-100 text-blue-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {note.type.charAt(0).toUpperCase() + note.type.slice(1)}
                    </span>
                  </TableCell>
                  <TableCell>
                    {note.student ? (
                      <button 
                        className="flex items-center text-blue-600 hover:underline"
                        onClick={() => note.student && onStudentSelect(note.student.id)}
                      >
                        <User className="mr-1 h-3 w-3" />
                        {note.student.name}
                      </button>
                    ) : (
                      "-"
                    )}
                  </TableCell>
                  <TableCell>
                    {note.relatedClass ? (
                      <button 
                        className="flex items-center text-blue-600 hover:underline"
                        onClick={() => note.relatedClass && onClassSelect(note.relatedClass.id)}
                      >
                        <BookOpen className="mr-1 h-3 w-3" />
                        {note.relatedClass.name}
                      </button>
                    ) : (
                      "-"
                    )}
                  </TableCell>
                  <TableCell>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                          <span className="sr-only">Actions</span>
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleNoteClick(note.id)}>
                          <FileText className="mr-2 h-4 w-4" />
                          View Note
                        </DropdownMenuItem>
                        {note.student && (
                          <DropdownMenuItem onClick={() => onStudentSelect(note.student!.id)}>
                            <User className="mr-2 h-4 w-4" />
                            Student Notes
                          </DropdownMenuItem>
                        )}
                        {note.relatedClass && (
                          <DropdownMenuItem onClick={() => onClassSelect(note.relatedClass!.id)}>
                            <BookOpen className="mr-2 h-4 w-4" />
                            Class Notes
                          </DropdownMenuItem>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
      
      {selectedNote && (
        <NoteDetailDialog
          note={selectedNote}
          open={!!selectedNoteId}
          onOpenChange={(open) => {
            if (!open) setSelectedNoteId(null);
          }}
          onNoteUpdated={() => refetch()}
        />
      )}
    </div>
  );
};

export default NotesTable;
