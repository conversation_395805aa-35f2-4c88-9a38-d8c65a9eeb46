
import { Student, StudentFilter, StudentApiResponse, StudentDetailsApiResponse, Payment, ClassAssignment } from "@/types/student";

/**
 * Fetch students with filtering options
 */
export const fetchStudents = async (filters: Partial<StudentFilter> = {}): Promise<StudentApiResponse> => {
  try {
    // Build query parameters
    const params = new URLSearchParams();
    if (filters.search) params.append('search', filters.search);
    if (filters.status) params.append('status', filters.status);
    if (filters.level) params.append('level', filters.level);
    if (filters.classId) params.append('classId', filters.classId);
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());

    const response = await fetch(`http://localhost:3000/api/students?${params}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      data: data.data || [],
      pagination: data.pagination || {
        total: 0,
        page: 1,
        limit: 10,
        pages: 0
      }
    };
  } catch (error) {
    console.error('Error fetching students:', error);
    return {
      success: false,
      data: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10,
        pages: 0
      }
    };
  }
};

/**
 * Fetch a student by ID
 */

/**
 * Fetch a student by ID
 */
export const fetchStudent = async (studentId: string): Promise<StudentDetailsApiResponse> => {
  try {
    const response = await fetch(`http://localhost:3000/api/students/${studentId}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      data: data.data
    };
  } catch (error) {
    console.error('Error fetching student:', error);
    return {
      success: false,
      data: null
    };
  }
};

/**
 * Create a new student
 */
export const createStudent = async (studentData: Partial<Student>): Promise<StudentDetailsApiResponse> => {
  try {
    console.log('Creating student with data:', studentData);

    const response = await fetch('http://localhost:3000/api/students', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(studentData),
    });

    const data = await response.json();
    console.log('Response from server:', data);

    if (!response.ok) {
      console.error('Server error:', data);
      throw new Error(data.message || `HTTP error! status: ${response.status}`);
    }

    return {
      success: true,
      data: data.data
    };
  } catch (error) {
    console.error('Error creating student:', error);
    return {
      success: false,
      data: null,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
};

/**
 * Update a student
 */
export const updateStudent = async (studentId: string, studentData: Partial<Student>): Promise<StudentDetailsApiResponse> => {
  try {
    const response = await fetch(`http://localhost:3000/api/students/${studentId}`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(studentData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      data: data.data
    };
  } catch (error) {
    console.error('Error updating student:', error);
    return {
      success: false,
      data: null
    };
  }
};

/**
 * Transfer student to another class
 */
export const transferStudent = async (
  studentId: string,
  toClassId: string,
  reason: string,
  transferDate?: string
): Promise<StudentDetailsApiResponse> => {
  try {
    const response = await fetch(`http://localhost:3000/api/students/${studentId}/transfer`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        toClass: toClassId,
        reason,
        transferDate
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      data: data.data
    };
  } catch (error) {
    console.error('Error transferring student:', error);
    return {
      success: false,
      data: null
    };
  }
};

/**
 * Record student payment
 */
export const recordPayment = async (
  studentId: string,
  paymentData: Omit<Payment, 'id' | 'status'>
): Promise<StudentDetailsApiResponse> => {
  try {
    const response = await fetch(`http://localhost:3000/api/students/${studentId}/payments`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(paymentData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      data: data.data
    };
  } catch (error) {
    console.error('Error recording payment:', error);
    return {
      success: false,
      data: null
    };
  }
};

/**
 * Get student suggested classes based on level
 */
export const getSuggestedClasses = async (studentId: string): Promise<{id: string, name: string}[]> => {
  try {
    const response = await fetch(`http://localhost:3000/api/students/${studentId}/suggested-classes`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.data || [];
  } catch (error) {
    console.error('Error fetching suggested classes:', error);
    return [];
  }
};

/**
 * Archive a student
 */
export const archiveStudent = async (studentId: string): Promise<{ success: boolean; data?: any }> => {
  try {
    const response = await fetch(`http://localhost:3000/api/students/${studentId}/archive`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      data: data.data
    };
  } catch (error) {
    console.error('Error archiving student:', error);
    return {
      success: false
    };
  }
};

/**
 * Restore a student
 */
export const restoreStudent = async (studentId: string): Promise<{ success: boolean; data?: any }> => {
  try {
    const response = await fetch(`http://localhost:3000/api/students/${studentId}/restore`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      data: data.data
    };
  } catch (error) {
    console.error('Error restoring student:', error);
    return {
      success: false
    };
  }
};
