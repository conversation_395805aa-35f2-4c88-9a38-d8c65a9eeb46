// server/src/services/class.service.ts
import mongoose, { FilterQuery } from 'mongoose';
import { Class } from '../models/class.model';
import { User } from '../models/user.model';
import { Student } from '../models/student.model';
import { AppError } from '../types/error.types';
import { SystemLogger } from './logger.service';
import {
    IClass,
    ClassQueryOptions,
    CreateClassDTO,
    UpdateClassDTO,
    ScheduleMakeupClassDTO,
    MergeClassDTO,
    ClassBulkOperationDTO,
    ClassExportOptions,
    TeacherClassSchedule,
    RoomAvailability,
    ClassStatistics,
    ClassResponseDTO,
    TeacherSchedule,
    TeacherReplacementDTO,
    SplitClassDTO,
    TeacherTransitionDTO
} from '../types/class.types';

export class ClassService {
    static async getClasses(
        options: ClassQueryOptions = {},
        requestingUserId: string
    ) {
        try {
            const {
                page = 1,
                limit = 10,
                sortBy = 'name',
                sortOrder = 'asc',
                status,
                level,
                teacherId,
                search,
                room,
                fromDate,
                toDate,
                hasCapacity
            } = options;

            const query: FilterQuery<IClass> = {};

            // Build query filters
            if (status) {
                query.status = status;
            }

            if (level) {
                query.level = level;
            }

            if (teacherId) {
                query['teachers.teacherId'] = new mongoose.Types.ObjectId(teacherId);
            }

            if (search) {
                query.$or = [
                    { name: { $regex: search, $options: 'i' } },
                    { level: { $regex: search, $options: 'i' } },
                    { room: { $regex: search, $options: 'i' } }
                ];
            }

            if (room) {
                query.room = room;
            }

            if (fromDate || toDate) {
                query.startDate = {};
                if (fromDate) query.startDate.$gte = fromDate;
                if (toDate) query.startDate.$lte = toDate;
            }

            if (hasCapacity) {
                query.$expr = { $lt: ['$currentStudentCount', '$capacity'] };
            }

            const skip = (page - 1) * limit;
            const sort: { [key: string]: 'asc' | 'desc' } = {
                [sortBy]: sortOrder
            };

            const [classes, total] = await Promise.all([
                Class.find(query)
                    .populate('teachers.teacherId', 'username')
                    .sort(sort)
                    .skip(skip)
                    .limit(limit),
                Class.countDocuments(query)
            ]);

            await SystemLogger.log({
                severity: 'info',
                category: 'class_management',
                action: 'list_classes',
                performedBy: requestingUserId,
                details: { filters: options },
                status: 'success',
                timestamp: new Date()
            });

            return {
                classes: classes.map(cls => this.formatClassResponse(cls)),
                pagination: {
                    total,
                    page,
                    limit,
                    pages: Math.ceil(total / limit)
                }
            };
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error fetching classes');
        }
    }

    private static getWeekDay(date: Date): TeacherSchedule['day'] {
        const days: { [key: string]: TeacherSchedule['day'] } = {
            'sunday': 'sunday',
            'monday': 'monday',
            'tuesday': 'tuesday',
            'wednesday': 'wednesday',
            'thursday': 'thursday',
            'friday': 'friday',
            'saturday': 'saturday'
        };
        return days[date.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase()];
    }

    private static handleError(err: unknown, defaultMessage: string): never {
        const error = err as Error;
        if (error instanceof AppError) {
            throw error;
        }
        throw new AppError(500, `${defaultMessage}: ${error.message}`);
    }

    private static handleOperationError(err: unknown, operation: string): never {
        return this.handleError(err, `Error ${operation} class`);
    }

    static async getClassById(id: string, requestingUserId: string) {
        const classDoc = await Class.findById(id)
            .populate('teachers.teacherId', 'username')
            .populate({
                path: 'studentHistory.studentId',
                select: 'name contactInfo'
            });

        if (!classDoc) {
            throw new AppError(404, 'Class not found');
        }

        await SystemLogger.log({
            severity: 'info',
            category: 'class_management',
            action: 'view_class',
            performedBy: requestingUserId,
            targetId: id,
            details: { className: classDoc.name },
            status: 'success',
            timestamp: new Date()
        });

        return this.formatClassResponse(classDoc);
    }

    static async createClass(
        classData: CreateClassDTO,
        createdBy: string
    ) {
        try {
            // Validate teachers exist and roles
            const teacherIds = classData.teachers.map(t => t.teacherId);
            const teachers = await User.find({
                _id: { $in: teacherIds },
                role: 'teacher',
                status: 'active'
            });

            if (teachers.length !== teacherIds.length) {
                throw new AppError(400, `Found ${teachers.length} teachers out of ${teacherIds.length} requested`);
            }

            // Check for schedule conflicts
            for (const teacher of classData.teachers) {
                try {
                    const hasConflict = await Class.validateScheduleConflicts(
                        new mongoose.Types.ObjectId(teacher.teacherId),
                        teacher.schedule
                    );

                    if (hasConflict) {
                        throw new AppError(400, `Schedule conflict detected for teacher ${teacher.teacherId}`);
                    }
                } catch (err) {
                    this.handleError(err, `Error validating schedule for teacher ${teacher.teacherId}`);
                }
            }

            // Check room availability
            const schedules = classData.teachers.flatMap(t => t.schedule);
            const availableRooms = await Class.findAvailableRooms(schedules);

            if (!availableRooms.includes(classData.room)) {
                throw new AppError(400, `Room ${classData.room} is not available for the specified schedule. Available rooms: ${availableRooms.join(', ')}`);
            }

            const newClass = new Class({
                ...classData,
                teachers: classData.teachers.map(t => ({
                    ...t,
                    teacherId: new mongoose.Types.ObjectId(t.teacherId)
                })),
                currentStudentCount: 0
            });

            await newClass.save();
            const populatedClass = await newClass.populate('teachers.teacherId', 'username');
            return this.formatClassResponse(populatedClass);

        } catch (err) {
            this.handleOperationError(err, 'creating');
        }
    }

    static async updateClass(
        id: string,
        updateData: UpdateClassDTO,
        updatedBy: string
    ) {
        try {
            const classDoc = await Class.findById(id);
            if (!classDoc) {
                throw new AppError(404, 'Class not found');
            }

            // If it's just a status update, handle it separately
            if (updateData.status && Object.keys(updateData).length === 1) {
                classDoc.status = updateData.status;
                await classDoc.save();
                return this.formatClassResponse(
                    await classDoc.populate('teachers.teacherId', 'username')
                );
            }

            // Handle regular updates with teacher/schedule changes
            if (updateData.teachers) {
                const teacherIds = updateData.teachers.map(t => t.teacherId);
                const teachers = await User.find({
                    _id: { $in: teacherIds },
                    role: 'teacher',
                    status: 'active'
                });

                if (teachers.length !== teacherIds.length) {
                    throw new AppError(400, 'One or more teachers not found or inactive');
                }

                for (const teacher of updateData.teachers) {
                    const hasConflict = await Class.validateScheduleConflicts(
                        new mongoose.Types.ObjectId(teacher.teacherId),
                        teacher.schedule,
                        classDoc._id
                    );

                    if (hasConflict) {
                        throw new AppError(400, 'Teacher schedule conflict detected');
                    }
                }
            }

            if (updateData.room) {
                const schedules = (updateData.teachers || classDoc.teachers).flatMap(t => t.schedule);
                const availableRooms = await Class.findAvailableRooms(schedules);

                if (!availableRooms.includes(updateData.room)) {
                    throw new AppError(400, 'Room is not available for the specified schedule');
                }
            }

            if (updateData.capacity && updateData.capacity < classDoc.currentStudentCount) {
                throw new AppError(400, 'New capacity cannot be less than current student count');
            }

            // Apply updates
            Object.assign(classDoc, {
                ...updateData,
                teachers: updateData.teachers?.map(t => ({
                    ...t,
                    teacherId: new mongoose.Types.ObjectId(t.teacherId)
                })) || classDoc.teachers
            });

            await classDoc.save();

            await SystemLogger.log({
                severity: 'info',
                category: 'class_management',
                action: 'update_class',
                performedBy: updatedBy,
                targetId: id,
                details: {
                    updates: updateData
                },
                status: 'success',
                timestamp: new Date()
            });

            return this.formatClassResponse(
                await classDoc.populate('teachers.teacherId', 'username')
            );
        } catch (error) {
            this.handleOperationError(error, 'updating');
        }
    }


    static async scheduleMakeupClass(
        id: string,
        makeupData: ScheduleMakeupClassDTO,
        scheduledBy: string
    ) {
        try {
            const classDoc = await Class.findById(id);
            if (!classDoc) {
                throw new AppError(404, 'Class not found');
            }

            const teacher = classDoc.teachers.find(t =>
                t.teacherId.toString() === makeupData.teacherId
            );
            if (!teacher) {
                throw new AppError(400, 'Teacher is not assigned to this class');
            }

            const hasConflict = await Class.validateScheduleConflicts(
                new mongoose.Types.ObjectId(makeupData.teacherId),
                [{
                    day: this.getWeekDay(new Date(makeupData.makeupDate)),
                    timeStart: new Date(makeupData.makeupDate).toLocaleTimeString('en-US', { hour12: false }),
                    timeEnd: new Date(makeupData.makeupDate).toLocaleTimeString('en-US', { hour12: false })
                }],
                classDoc._id
            );

            if (hasConflict) {
                throw new AppError(400, 'Teacher is not available at the specified time');
            }

            await classDoc.scheduleMakeupClass({
                ...makeupData,
                approvedBy: new mongoose.Types.ObjectId(scheduledBy)
            });

            return this.formatClassResponse(
                await classDoc.populate('teachers.teacherId', 'username')
            );
        } catch (err) {
            this.handleOperationError(err, 'scheduling makeup class');
        }
    }

    static async mergeClasses(
        sourceId: string,
        mergeData: MergeClassDTO,
        performedBy: string
    ) {
        try {
            const [sourceClass, targetClass] = await Promise.all([
                Class.findById(sourceId),
                Class.findById(mergeData.targetClassId)
            ]);

            if (!sourceClass || !targetClass) {
                throw new AppError(404, 'One or both classes not found');
            }

            if (sourceClass.level !== targetClass.level) {
                throw new AppError(400, 'Cannot merge classes of different levels');
            }

            await sourceClass.merge(
                new mongoose.Types.ObjectId(mergeData.targetClassId),
                mergeData.reason
            );

            return this.formatClassResponse(
                await sourceClass.populate('teachers.teacherId', 'username')
            );
        } catch (err) {
            this.handleOperationError(err, 'merging');
        }
    }

    static async bulkOperation(
        operation: ClassBulkOperationDTO,
        performedBy: string
    ) {
        try {
            const results = await Promise.allSettled(
                operation.classIds.map(async (classId) => {
                    const classDoc = await Class.findById(classId);
                    if (!classDoc) {
                        throw new AppError(404, `Class not found: ${classId}`);
                    }

                    switch (operation.operation) {
                        case 'activate':
                        case 'deactivate':
                            classDoc.status = operation.operation === 'activate' ? 'active' : 'inactive';
                            break;
                        case 'changeLevel':
                            classDoc.level = operation.newValue!;
                            break;
                        case 'changeRoom':
                            const availableRooms = await Class.findAvailableRooms(
                                classDoc.teachers.flatMap(t => t.schedule)
                            );
                            if (!availableRooms.includes(operation.newValue!)) {
                                throw new AppError(400, `Room ${operation.newValue} is not available`);
                            }
                            classDoc.room = operation.newValue!;
                            break;
                    }

                    return classDoc.save();
                })
            );

            const summary = {
                total: operation.classIds.length,
                successful: results.filter(r => r.status === 'fulfilled').length,
                failed: results.filter(r => r.status === 'rejected').length,
                errors: results
                    .map((r, i) => r.status === 'rejected' ?
                        { classId: operation.classIds[i], error: (r as PromiseRejectedResult).reason.message } :
                        null)
                    .filter(Boolean)
            };

            if (summary.failed === operation.classIds.length) {
                throw new AppError(400, 'All operations failed');
            }

            return summary;
        } catch (err) {
            this.handleOperationError(err, 'performing bulk operation');
        }
    }

    static async exportClasses(
        options: ClassExportOptions,
        exportedBy: string
    ): Promise<string> {
        try {
            const query: FilterQuery<IClass> = {};

            if (options.dateRange) {
                query.startDate = {
                    $gte: options.dateRange.start,
                    $lte: options.dateRange.end
                };
            }

            const classes = await Class.find(query)
                .populate('teachers.teacherId', 'username')
                .sort({ name: 1 });

            const exportData = classes.map(classDoc => {
                const baseData = this.formatClassResponse(classDoc);

                // Add optional data
                if (options.includeStudentHistory) {
                    (baseData as any).studentHistory = classDoc.studentHistory;
                }
                if (options.includeMakeupClasses) {
                    (baseData as any).makeupClasses = classDoc.makeupClasses;
                }

                return baseData;
            });

            if (options.format === 'json') {
                return JSON.stringify(exportData, null, 2);
            }

            // CSV format
            const fields = options.fields || Object.keys(exportData[0]);
            const header = fields.join(',');
            const rows = exportData.map(cls =>
                fields.map(field => this.formatCSVValue(this.getNestedValue(cls, field)))
                    .join(',')
            );

            return [header, ...rows].join('\n');
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error exporting classes');
        }
    }

    static async getClassSchedule(
        id: string,
        includeMakeup: boolean,
        requestingUserId: string
    ) {
        const classDoc = await Class.findById(id)
            .populate('teachers.teacherId', 'username')
            .populate({
                path: 'makeupClasses.approvedBy',
                select: 'username'
            });

        if (!classDoc) {
            throw new AppError(404, 'Class not found');
        }

        const schedule = {
            regularSchedule: classDoc.teachers.map(teacher => ({
                teacherId: teacher.teacherId._id,
                teacherName: (teacher.teacherId as any).username,
                schedule: teacher.schedule
            }))
        };

        if (includeMakeup) {
            (schedule as any).makeupClasses = classDoc.makeupClasses.map(makeup => ({
                originalDate: makeup.originalDate,
                makeupDate: makeup.makeupDate,
                status: makeup.status,
                approvedBy: (makeup.approvedBy as any).username
            }));
        }

        return schedule;
    }

    static async getRoomAvailability(
        room: string,
        date: Date,
        requestingUserId: string
    ): Promise<RoomAvailability> {
        const dayOfWeek = this.getWeekDay(date);

        const classes = await Class.find({
            room,
            status: 'active',
            'teachers.schedule.day': dayOfWeek
        }).populate('teachers.teacherId', 'username');

        // Initialize slots with correct typing for optional properties
        const daySlots = Array.from({ length: 24 }, (_, hour) => ({
            timeStart: `${hour.toString().padStart(2, '0')}:00`,
            timeEnd: `${hour.toString().padStart(2, '0')}:59`,
            isAvailable: true
        } as RoomAvailability['schedule'][0]['slots'][0]));  // Use interface type for correct typing

        // Mark occupied slots
        classes.forEach(cls => {
            cls.teachers.forEach(teacher => {
                teacher.schedule
                    .filter(s => s.day === dayOfWeek)
                    .forEach(schedule => {
                        const startHour = parseInt(schedule.timeStart.split(':')[0]);
                        const endHour = parseInt(schedule.timeEnd.split(':')[0]);

                        for (let hour = startHour; hour <= endHour; hour++) {
                            daySlots[hour] = {
                                timeStart: daySlots[hour].timeStart,
                                timeEnd: daySlots[hour].timeEnd,
                                isAvailable: false,
                                classId: cls._id.toString(),
                                className: cls.name
                            };
                        }
                    });
            });
        });

        return {
            room,
            date,
            schedule: [{
                day: dayOfWeek,
                slots: daySlots
            }]
        };
    }

    static async getTeacherSchedule(
        teacherId: string,
        dateRange: { fromDate?: Date; toDate?: Date },
        requestingUserId: string
    ): Promise<TeacherClassSchedule> {
        const teacher = await User.findOne({
            _id: teacherId,
            role: 'teacher',
            status: 'active'
        });

        if (!teacher) {
            throw new AppError(404, 'Teacher not found');
        }

        const query: FilterQuery<IClass> = {
            'teachers.teacherId': new mongoose.Types.ObjectId(teacherId),
            status: 'active'
        };

        if (dateRange.fromDate || dateRange.toDate) {
            query.startDate = {};
            if (dateRange.fromDate) query.startDate.$gte = dateRange.fromDate;
            if (dateRange.toDate) query.startDate.$lte = dateRange.toDate;
        }

        const classes = await Class.find(query);

        const schedule: { [key in TeacherSchedule['day']]: any[] } = {
            monday: [], tuesday: [], wednesday: [], thursday: [], friday: [], saturday: [], sunday: []
        };

        let totalHours = 0;
        classes.forEach(cls => {
            const teacherSchedule = cls.teachers.find(t =>
                t.teacherId.toString() === teacherId
            );

            if (teacherSchedule) {
                teacherSchedule.schedule.forEach(slot => {
                    schedule[slot.day].push({
                        classId: cls._id.toString(),
                        className: cls.name,
                        timeStart: slot.timeStart,
                        timeEnd: slot.timeEnd,
                        room: cls.room
                    });

                    // Calculate hours
                    const start = new Date(`1970-01-01T${slot.timeStart}`);
                    const end = new Date(`1970-01-01T${slot.timeEnd}`);
                    totalHours += (end.getTime() - start.getTime()) / (1000 * 60 * 60);
                });
            }
        });

        return {
            teacherId,
            schedule: Object.entries(schedule).map(([day, classes]) => ({
                day: day as TeacherSchedule['day'],
                classes: classes.sort((a, b) => a.timeStart.localeCompare(b.timeStart))
            })),
            totalHours,
            classCount: classes.length
        };
    }

    static async getClassStatistics(
        options: {
            fromDate?: Date;
            toDate?: Date;
            level?: string;
        },
        requestingUserId: string
    ): Promise<ClassStatistics> {
        const query: FilterQuery<IClass> = { status: 'active' };

        if (options.level) {
            query.level = options.level;
        }

        if (options.fromDate || options.toDate) {
            query.startDate = {};
            if (options.fromDate) query.startDate.$gte = options.fromDate;
            if (options.toDate) query.startDate.$lte = options.toDate;
        }

        const classes = await Class.find(query);

        const totalClasses = classes.length;
        const activeClasses = classes.length;
        const totalCapacity = classes.reduce((sum, cls) => sum + cls.capacity, 0);
        const totalStudents = classes.reduce((sum, cls) => sum + cls.currentStudentCount, 0);
        const averageCapacityUtilization = totalClasses ? (totalStudents / totalCapacity) * 100 : 0;

        const classesAtCapacity = classes.filter(cls =>
            cls.currentStudentCount === cls.capacity
        ).length;

        const classesNearCapacity = classes.filter(cls =>
            cls.currentStudentCount >= cls.capacity * 0.9
        ).length;

        const averageClassSize = totalClasses ? totalStudents / totalClasses : 0;

        // Calculate level distribution
        const levelCounts = classes.reduce((acc, cls) => {
            acc[cls.level] = (acc[cls.level] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        const levelDistribution = Object.entries(levelCounts).map(([level, count]) => ({
            level,
            count
        }));

        return {
            totalClasses,
            activeClasses,
            averageCapacityUtilization,
            classesAtCapacity,
            classesNearCapacity,
            averageClassSize,
            levelDistribution
        };
    }

    static async replaceTeacher(
        id: string,
        replacementData: TeacherReplacementDTO,
        performedBy: string
    ): Promise<ClassResponseDTO> {
        try {
            const classDoc = await Class.findById(id);
            if (!classDoc) {
                throw new AppError(404, 'Class not found');
            }

            const originalTeacherIndex = classDoc.teachers.findIndex(
                t => t.teacherId.toString() === replacementData.originalTeacherId
            );

            if (originalTeacherIndex === -1) {
                throw new AppError(400, 'Original teacher is not assigned to this class');
            }

            const originalTeacher = classDoc.teachers[originalTeacherIndex];

            // Find existing new teacher in the class if already assigned
            let newTeacherIndex = classDoc.teachers.findIndex(
                t => t.teacherId.toString() === replacementData.newTeacherId
            );

            // Filter out schedules that are being replaced
            const remainingSchedules = originalTeacher.schedule.filter(schedule =>
                !replacementData.scheduleToReplace.some(
                    s => s.day === schedule.day &&
                        s.timeStart === schedule.timeStart &&
                        s.timeEnd === schedule.timeEnd
                )
            );

            // If the original teacher has no more schedules, remove them
            if (remainingSchedules.length === 0) {
                classDoc.teachers.splice(originalTeacherIndex, 1);
            } else {
                classDoc.teachers[originalTeacherIndex].schedule = remainingSchedules;
            }

            // Handle the new teacher assignment
            if (newTeacherIndex === -1) {
                // New teacher isn't already assigned to this class, add them
                classDoc.teachers.push({
                    teacherId: new mongoose.Types.ObjectId(replacementData.newTeacherId),
                    schedule: replacementData.scheduleToReplace
                });
            } else {
                // New teacher is already assigned, add the new schedules
                classDoc.teachers[newTeacherIndex].schedule = [
                    ...classDoc.teachers[newTeacherIndex].schedule,
                    ...replacementData.scheduleToReplace
                ];
            }

            // Record the replacement in history
            if (!classDoc.teacherReplacementHistory) {
                classDoc.teacherReplacementHistory = [];
            }

            classDoc.teacherReplacementHistory.push({
                originalTeacherId: new mongoose.Types.ObjectId(replacementData.originalTeacherId),
                newTeacherId: new mongoose.Types.ObjectId(replacementData.newTeacherId),
                replacementDate: replacementData.effectiveDate,
                reason: replacementData.reason,
                approvedBy: new mongoose.Types.ObjectId(performedBy),
                scheduleAffected: replacementData.scheduleToReplace
            });

            await classDoc.save();

            // Log the teacher replacement
            await SystemLogger.log({
                severity: 'info',
                category: 'class_management',
                action: 'teacher_replacement',
                performedBy,
                targetId: id,
                details: {
                    originalTeacherId: replacementData.originalTeacherId,
                    newTeacherId: replacementData.newTeacherId,
                    reason: replacementData.reason,
                    effectiveDate: replacementData.effectiveDate
                },
                status: 'success',
                timestamp: new Date()
            });

            return this.formatClassResponse(
                await classDoc.populate('teachers.teacherId', 'username')
            );
        } catch (error) {
            this.handleOperationError(error, 'replacing teacher');
        }
    }

    static async splitClass(
        sourceId: string,
        splitData: SplitClassDTO,
        performedBy: string
    ): Promise<{ sourceClass: ClassResponseDTO; newClass: ClassResponseDTO }> {
        try {
            // Find source class
            const sourceClass = await Class.findById(sourceId);
            if (!sourceClass) {
                throw new AppError(404, 'Source class not found');
            }

            // Validate split data
            if (splitData.studentIds.length === 0) {
                throw new AppError(400, 'No students selected for splitting');
            }

            // Verify all students exist in the source class
            const studentsToMove = await Student.find({
                _id: { $in: splitData.studentIds.map(id => new mongoose.Types.ObjectId(id)) },
                currentClass: sourceId,
                status: 'active'
            });

            if (studentsToMove.length !== splitData.studentIds.length) {
                throw new AppError(400, 'One or more selected students are not in this class');
            }

            // TEACHER ASSIGNMENT VALIDATION
            // Verify we have teacher assignments that are different from the source class
            if (!splitData.teacherAssignments || splitData.teacherAssignments.length === 0) {
                throw new AppError(400, 'Teacher assignments are required for the new class');
            }

            // Check if teacher assignments are valid (either different teachers or different schedules)
            const isDifferentTeacher = splitData.teacherAssignments.some(newTeacher =>
                !sourceClass.teachers.some(existingTeacher =>
                    existingTeacher.teacherId.toString() === newTeacher.teacherId.toString()
                )
            );

            const isDifferentSchedule = !this.hasScheduleConflict(
                sourceClass.teachers,
                splitData.teacherAssignments
            );

            if (!isDifferentTeacher && !isDifferentSchedule) {
                throw new AppError(400,
                    'When splitting a class, you must provide either different teachers or different schedules'
                );
            }

            // Create new class with the specified teacher assignments
            const newClass = new Class({
                name: splitData.name,
                level: splitData.level || sourceClass.level,
                teachers: splitData.teacherAssignments.map(t => ({
                    teacherId: new mongoose.Types.ObjectId(t.teacherId.toString()),
                    schedule: t.schedule
                })),
                room: splitData.room,
                capacity: splitData.capacity,
                currentStudentCount: 0,
                startDate: sourceClass.startDate,
                endDate: sourceClass.endDate,
                status: 'active'
            });

            await newClass.save();

            // Transfer students to new class
            const now = new Date();
            const splitReason = `Class split: ${splitData.reason}`;

            // For each student, update their class history
            for (const student of studentsToMove) {
                // Update history in source class
                const studentHistoryEntry = sourceClass.studentHistory.find(
                    (history) => history.studentId.equals(student._id as unknown as mongoose.Types.ObjectId) && !history.leaveDate
                );

                if (studentHistoryEntry) {
                    studentHistoryEntry.leaveDate = now;
                    studentHistoryEntry.reason = splitReason;
                }

                // Add to new class history
                newClass.studentHistory.push({
                    studentId: new mongoose.Types.ObjectId(String(student._id)),
                    joinDate: now,
                    reason: splitReason
                });

                // Update student document
                student.classHistory.push({
                    classId: newClass._id,
                    startDate: now,
                    reason: splitReason
                });
                student.currentClass = newClass._id;
                await student.save();

                // Update counts
                newClass.currentStudentCount += 1;
                sourceClass.currentStudentCount -= 1;
            }

            await Promise.all([sourceClass.save(), newClass.save()]);

            // Log the split operation
            await SystemLogger.log({
                severity: 'info',
                category: 'class_management',
                action: 'split_class',
                performedBy: performedBy,
                targetId: sourceId,
                details: {
                    newClassId: newClass._id,
                    transferredStudents: splitData.studentIds.length,
                    reason: splitData.reason
                },
                status: 'success',
                timestamp: new Date()
            });

            return {
                sourceClass: this.formatClassResponse(
                    await sourceClass.populate('teachers.teacherId', 'username')
                ),
                newClass: this.formatClassResponse(
                    await newClass.populate('teachers.teacherId', 'username')
                )
            };
        } catch (err) {
            this.handleOperationError(err, 'splitting');
        }
    }

    static async initiateTeacherTransition(
        classId: string,
        transitionData: TeacherTransitionDTO,
        initiatedBy: string
    ): Promise<ClassResponseDTO> {
        try {
            const classDoc = await Class.findById(classId);
            if (!classDoc) {
                throw new AppError(404, 'Class not found');
            }

            // Verify both teachers exist
            const [oldTeacher, newTeacher] = await Promise.all([
                User.findById(transitionData.oldTeacherId),
                User.findById(transitionData.newTeacherId)
            ]);

            if (!oldTeacher || !newTeacher) {
                throw new AppError(404, 'One or both teachers not found');
            }

            if (oldTeacher.role !== 'teacher' || newTeacher.role !== 'teacher') {
                throw new AppError(400, 'Both users must have teacher role');
            }

            // Check if old teacher is assigned to the class
            const oldTeacherIndex = classDoc.teachers.findIndex(
                t => t.teacherId.toString() === transitionData.oldTeacherId
            );

            if (oldTeacherIndex === -1) {
                throw new AppError(400, 'Old teacher is not assigned to this class');
            }

            // Add teacher transition record
            classDoc.teacherTransitions.push({
                oldTeacherId: new mongoose.Types.ObjectId(transitionData.oldTeacherId),
                newTeacherId: new mongoose.Types.ObjectId(transitionData.newTeacherId),
                startDate: transitionData.transitionStartDate,
                endDate: transitionData.transitionEndDate,
                status: 'pending',
                notes: transitionData.notes,
                materials: transitionData.materialTransfer ? [{
                    note: transitionData.materialTransfer.notes,
                    uploadedAt: new Date()
                }] : []
            });

            // If transition starts immediately, update teacher assignments
            if (transitionData.transitionStartDate <= new Date()) {
                // Replace schedules for the old teacher
                const oldTeacherSchedules = classDoc.teachers[oldTeacherIndex].schedule;
                const schedulesToTransfer = transitionData.schedulesToTransfer;

                // Keep only schedules not being transferred
                classDoc.teachers[oldTeacherIndex].schedule = oldTeacherSchedules.filter(
                    s => !schedulesToTransfer.some(ts =>
                        ts.day === s.day && ts.timeStart === s.timeStart && ts.timeEnd === s.timeEnd
                    )
                );

                // Check if new teacher is already assigned to this class
                const newTeacherIndex = classDoc.teachers.findIndex(
                    t => t.teacherId.toString() === transitionData.newTeacherId
                );

                if (newTeacherIndex !== -1) {
                    // New teacher already assigned, add schedules
                    classDoc.teachers[newTeacherIndex].schedule = [
                        ...classDoc.teachers[newTeacherIndex].schedule,
                        ...schedulesToTransfer
                    ];
                } else {
                    // New teacher not assigned yet, add new entry
                    classDoc.teachers.push({
                        teacherId: new mongoose.Types.ObjectId(transitionData.newTeacherId),
                        schedule: schedulesToTransfer
                    });
                }
            }

            await classDoc.save();

            // If requested, send notifications to affected students
            if (transitionData.notifyStudents) {
                const activeStudents = await Student.find({
                    currentClass: classDoc._id,
                    status: 'active'
                });

                // In a real system, you would send actual notifications here
                // For now, just log the notification
                await SystemLogger.log({
                    severity: 'info',
                    category: 'notifications',
                    action: 'teacher_transition_notification',
                    performedBy: initiatedBy,
                    details: {
                        classId,
                        oldTeacher: oldTeacher.username,
                        newTeacher: newTeacher.username,
                        affectedStudents: activeStudents.length
                    },
                    status: 'success',
                    timestamp: new Date()
                });
            }

            return this.formatClassResponse(
                await classDoc.populate([
                    { path: 'teachers.teacherId', select: 'username' },
                    { path: 'teacherTransitions.oldTeacherId', select: 'username' },
                    { path: 'teacherTransitions.newTeacherId', select: 'username' }
                ])
            );
        } catch (err) {
            this.handleOperationError(err, 'initiating teacher transition');
        }
    }

    private static formatClassResponse(classDoc: any): ClassResponseDTO {
        return {
            id: classDoc._id.toString(),
            name: classDoc.name,
            level: classDoc.level,
            teachers: classDoc.teachers.map((teacher: any) => ({
                id: teacher.teacherId._id.toString(),
                name: teacher.teacherId.username,
                schedule: teacher.schedule
            })),
            room: classDoc.room,
            capacity: {
                total: classDoc.capacity,
                current: classDoc.currentStudentCount,
                available: classDoc.capacity - classDoc.currentStudentCount
            },
            schedule: {
                startDate: classDoc.startDate,
                endDate: classDoc.endDate,
                days: this.aggregateScheduleByDay(classDoc.teachers)
            },
            status: classDoc.status
        };
    }

    private static aggregateScheduleByDay(teachers: any[]) {
        const days: Record<TeacherSchedule['day'], {
            start: string;
            end: string;
            teacher: string;
        }[]> = {
            monday: [],
            tuesday: [],
            wednesday: [],
            thursday: [],
            friday: [],
            saturday: [],
            sunday: []
        };

        teachers.forEach(teacher => {
            teacher.schedule.forEach((slot: TeacherSchedule) => {
                days[slot.day].push({
                    start: slot.timeStart,
                    end: slot.timeEnd,
                    teacher: teacher.teacherId.username
                });
            });
        });

        return Object.entries(days).map(([day, times]) => ({
            day: day as TeacherSchedule['day'],
            times: times.sort((a, b) => a.start.localeCompare(b.start))
        }));
    }

    private static getNestedValue(obj: any, path: string): any {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    }

    private static formatCSVValue(value: any): string {
        if (value === null || value === undefined) return '';
        if (value instanceof Date) return value.toISOString();
        if (typeof value === 'object') return JSON.stringify(value);
        return `"${String(value).replace(/"/g, '""')}"`;
    }

    private static hasScheduleConflict(
        sourceTeachers: Array<{ teacherId: mongoose.Types.ObjectId; schedule: TeacherSchedule[] }>,
        newTeachers: Array<{ teacherId: mongoose.Types.ObjectId | string; schedule: TeacherSchedule[] }>
    ): boolean {
        // For each source teacher
        for (const sourceTeacher of sourceTeachers) {
            // Find matching teacher in new assignments
            const matchingTeacher = newTeachers.find(t => 
                t.teacherId.toString() === sourceTeacher.teacherId.toString()
            );
            
            if (matchingTeacher) {
                // Check if schedules overlap
                for (const sourceSchedule of sourceTeacher.schedule) {
                    for (const newSchedule of matchingTeacher.schedule) {
                        if (
                            sourceSchedule.day === newSchedule.day &&
                            (
                                (sourceSchedule.timeStart <= newSchedule.timeStart && 
                                 sourceSchedule.timeEnd > newSchedule.timeStart) ||
                                (sourceSchedule.timeStart < newSchedule.timeEnd && 
                                 sourceSchedule.timeEnd >= newSchedule.timeEnd)
                            )
                        ) {
                            return true; // Schedule conflict found
                        }
                    }
                }
            }
        }
        
        return false; // No conflicts found
    }

    static async getAvailableLevels(requestingUserId: string): Promise<string[]> {
        try {
            const levels = await Class.distinct('level', { status: 'active' });

            await SystemLogger.log({
                severity: 'info',
                category: 'class_management',
                action: 'get_available_levels',
                performedBy: requestingUserId,
                details: { levelsCount: levels.length },
                status: 'success',
                timestamp: new Date()
            });

            return levels.sort();
        } catch (err) {
            this.handleOperationError(err, 'fetching available levels');
            return [];
        }
    }

    static async getAvailableRooms(requestingUserId: string): Promise<string[]> {
        try {
            const rooms = await Class.distinct('room', { status: 'active' });

            await SystemLogger.log({
                severity: 'info',
                category: 'class_management',
                action: 'get_available_rooms',
                performedBy: requestingUserId,
                details: { roomsCount: rooms.length },
                status: 'success',
                timestamp: new Date()
            });

            return rooms.sort();
        } catch (err) {
            this.handleOperationError(err, 'fetching available rooms');
            return [];
        }
    }
}