import Joi from 'joi';
import { AttendanceStatus, ExcuseStatus } from '../types/attendance.types';

const attendanceStatusSchema = Joi.string()
    .valid('present', 'absent', 'late', 'excused')
    .required()
    .messages({
        'any.only': 'Invalid attendance status',
        'any.required': 'Attendance status is required'
    });

const optionalAttendanceStatusSchema = Joi.string()
    .valid('present', 'absent', 'late', 'excused')
    .messages({
        'any.only': 'Invalid attendance status'
    });

const excuseStatusSchema = Joi.string()
    .valid('pending', 'approved', 'rejected')
    .required()
    .messages({
        'any.only': 'Invalid excuse status',
        'any.required': 'Excuse status is required'
    });

const dateSchema = Joi.date()
    .iso()
    .required()
    .messages({
        'date.base': 'Invalid date format',
        'date.format': 'Date must be in ISO format',
        'any.required': 'Date is required'
    });

export const attendanceValidation = {
    // GET /api/attendance query parameters
    getAttendanceQuery: Joi.object({
        page: Joi.number()
            .min(1)
            .messages({
                'number.min': 'Page number must be greater than 0'
            }),
        limit: Joi.number()
            .min(1)
            .max(100)
            .messages({
                'number.min': 'Limit must be greater than 0',
                'number.max': 'Limit cannot exceed 100'
            }),
        sortBy: Joi.string()
            .valid('date', 'modifiedAt')
            .messages({
                'any.only': 'Invalid sort field'
            }),
        sortOrder: Joi.string()
            .valid('asc', 'desc')
            .messages({
                'any.only': 'Sort order must be either asc or desc'
            }),
        startDate: Joi.date().iso(),
        endDate: Joi.date()
            .iso()
            .min(Joi.ref('startDate'))
            .messages({
                'date.min': 'End date must be after start date'
            }),
        classId: Joi.string()
            .pattern(/^[0-9a-fA-F]{24}$/)
            .messages({
                'string.pattern.base': 'Invalid class ID format'
            }),
        teacherId: Joi.string()
            .pattern(/^[0-9a-fA-F]{24}$/)
            .messages({
                'string.pattern.base': 'Invalid teacher ID format'
            }),
        studentId: Joi.string()
            .pattern(/^[0-9a-fA-F]{24}$/)
            .messages({
                'string.pattern.base': 'Invalid student ID format'
            }),
        status: optionalAttendanceStatusSchema,
        isMakeupClass: Joi.boolean()
    }),

    // POST /api/attendance/classes/:classId
    markAttendance: Joi.object({
        date: dateSchema,
        studentId: Joi.string()
            .pattern(/^[0-9a-fA-F]{24}$/)
            .required()
            .messages({
                'string.pattern.base': 'Invalid student ID format',
                'any.required': 'Student ID is required'
            }),
        status: attendanceStatusSchema,
        arrivalTime: Joi.date()
            .iso()
            .when('status', {
                is: 'late',
                then: Joi.required(),
                otherwise: Joi.forbidden()
            })
            .messages({
                'any.required': 'Arrival time is required for late status'
            }),
        excuse: Joi.object({
            reason: Joi.string()
                .min(10)
                .max(500)
                .required()
                .messages({
                    'string.min': 'Excuse reason must be at least 10 characters',
                    'string.max': 'Excuse reason cannot exceed 500 characters',
                    'any.required': 'Excuse reason is required'
                }),
            documentUrl: Joi.string().uri()
        }).when('status', {
            is: 'absent',
            then: Joi.optional(),
            otherwise: Joi.forbidden()
        }),
        notes: Joi.string().max(500)
    }),

    // POST /api/attendance/classes/:classId/bulk
    bulkMarkAttendance: Joi.object({
        date: dateSchema,
        records: Joi.array()
            .items(Joi.object({
                studentId: Joi.string()
                    .pattern(/^[0-9a-fA-F]{24}$/)
                    .required()
                    .messages({
                        'string.pattern.base': 'Invalid student ID format',
                        'any.required': 'Student ID is required'
                    }),
                status: attendanceStatusSchema,
                arrivalTime: Joi.date()
                    .iso()
                    .when('status', {
                        is: 'late',
                        then: Joi.required(),
                        otherwise: Joi.forbidden()
                    }),
                excuse: Joi.object({
                    reason: Joi.string()
                        .min(10)
                        .max(500)
                        .required(),
                    documentUrl: Joi.string().uri()
                }).when('status', {
                    is: 'absent',
                    then: Joi.optional(),
                    otherwise: Joi.forbidden()
                }),
                notes: Joi.string().max(500)
            }))
            .min(1)
            .max(50)
            .unique('studentId')
            .required()
            .messages({
                'array.min': 'At least one attendance record is required',
                'array.max': 'Cannot process more than 50 records at once',
                'array.unique': 'Duplicate student IDs are not allowed',
                'any.required': 'Attendance records are required'
            })
    }),

    // POST /api/attendance/classes/:classId/excuse
    addExcuse: Joi.object({
        date: dateSchema,
        studentId: Joi.string()
            .pattern(/^[0-9a-fA-F]{24}$/)
            .required()
            .messages({
                'string.pattern.base': 'Invalid student ID format',
                'any.required': 'Student ID is required'
            }),
        reason: Joi.string()
            .min(10)
            .max(500)
            .required()
            .messages({
                'string.min': 'Excuse reason must be at least 10 characters',
                'string.max': 'Excuse reason cannot exceed 500 characters',
                'any.required': 'Excuse reason is required'
            }),
        documentUrl: Joi.string().uri(),
        notes: Joi.string().max(500)
    }),

    // POST /api/attendance/classes/:classId/students/:studentId/verify-excuse
    verifyExcuse: Joi.object({
        date: dateSchema,
        status: excuseStatusSchema,
        notes: Joi.string().max(500)
    }),

    // GET /api/attendance/students/:studentId/stats or /api/attendance/classes/:classId/stats
    getAttendanceStats: Joi.object({
        startDate: Joi.date()
            .iso()
            .required()
            .messages({
                'date.base': 'Invalid start date format',
                'any.required': 'Start date is required'
            }),
        endDate: Joi.date()
            .iso()
            .min(Joi.ref('startDate'))
            .required()
            .messages({
                'date.base': 'Invalid end date format',
                'date.min': 'End date must be after start date',
                'any.required': 'End date is required'
            })
    }),

    // GET /api/attendance/export
    exportAttendance: Joi.object({
        format: Joi.string()
            .valid('csv', 'json')
            .default('csv')
            .messages({
                'any.only': 'Export format must be either csv or json'
            }),
        startDate: Joi.date()
            .iso()
            .required()
            .messages({
                'date.base': 'Invalid start date format',
                'any.required': 'Start date is required'
            }),
        endDate: Joi.date()
            .iso()
            .min(Joi.ref('startDate'))
            .required()
            .messages({
                'date.min': 'End date must be after start date',
                'any.required': 'End date is required'
            }),
        includeStudentDetails: Joi.boolean(),
        includeExcuses: Joi.boolean(),
        includeMakeupClasses: Joi.boolean(),
        groupBy: Joi.string()
            .valid('date', 'student', 'class')
            .default('date')
            .messages({
                'any.only': 'Invalid grouping option'
            })
    }),

    // GET /api/attendance/students/:studentId/unified
    getUnifiedAttendance: Joi.object({
        startDate: Joi.date()
            .iso()
            .messages({
                'date.base': 'Invalid start date format',
                'date.format': 'Start date must be in ISO format'
            }),
        endDate: Joi.date()
            .iso()
            .min(Joi.ref('startDate'))
            .messages({
                'date.base': 'Invalid end date format',
                'date.min': 'End date must be after start date'
            })
    })
};