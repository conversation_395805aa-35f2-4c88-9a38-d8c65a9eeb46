// Quick MongoDB connection test
const mongoose = require('mongoose');

async function testConnection() {
    console.log('🔍 Testing MongoDB Connection...\n');
    
    const uri = 'mongodb://localhost:27017/vertex';
    const options = {
        autoIndex: true,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
        maxPoolSize: 10,
        minPoolSize: 5,
        maxIdleTimeMS: 30000,
    };
    
    try {
        console.log('Connecting to:', uri);
        await mongoose.connect(uri, options);
        console.log('✅ MongoDB connection successful!');
        
        // Test if users collection exists
        const collections = await mongoose.connection.db.listCollections().toArray();
        const userCollection = collections.find(c => c.name === 'users');
        
        if (userCollection) {
            console.log('✅ Users collection exists');
            
            // Count users
            const userCount = await mongoose.connection.db.collection('users').countDocuments();
            console.log(`📊 Found ${userCount} users in database`);
            
            if (userCount === 0) {
                console.log('⚠️  No users found - you need to run the seed script');
                console.log('   Run: cd server && npm run seed');
            } else {
                // Show first user
                const firstUser = await mongoose.connection.db.collection('users').findOne();
                console.log('👤 Sample user:', {
                    username: firstUser.username,
                    role: firstUser.role,
                    status: firstUser.status
                });
            }
        } else {
            console.log('⚠️  Users collection not found - database needs seeding');
        }
        
    } catch (error) {
        console.log('❌ MongoDB connection failed:', error.message);
        console.log('\nPossible solutions:');
        console.log('1. Start MongoDB service: net start MongoDB');
        console.log('2. Install MongoDB if not installed');
        console.log('3. Use MongoDB Atlas (cloud) instead');
    } finally {
        await mongoose.disconnect();
        console.log('\n🔌 Disconnected from MongoDB');
    }
}

testConnection();
