
import { useState } from "react";
import { toast } from "sonner";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useQuery } from "@tanstack/react-query";
import { getCurrentUser } from "@/lib/auth";

import { apiClient } from "@/lib/api";
import { createNote } from "@/services/notesService";

// Fetch students from API
const fetchStudents = async () => {
  try {
    const response = await apiClient.get('/students', {
      params: { limit: 100, sortBy: 'name', sortOrder: 'asc' }
    });
    return response.data.success ? response.data.data : [];
  } catch (error) {
    console.error('Error fetching students:', error);
    return [];
  }
};

// Fetch classes from API
const fetchClasses = async () => {
  try {
    const response = await apiClient.get('/classes', {
      params: { limit: 100, sortBy: 'name', sortOrder: 'asc' }
    });
    return response.data.success ? response.data.data : [];
  } catch (error) {
    console.error('Error fetching classes:', error);
    return [];
  }
};

interface CreateNoteDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onNoteCreated: () => void;
  initialStudentId?: string;
  initialClassId?: string;
}

const CreateNoteDialog = ({ 
  open, 
  onOpenChange, 
  onNoteCreated,
  initialStudentId,
  initialClassId
}: CreateNoteDialogProps) => {
  const [noteType, setNoteType] = useState("general");
  const [noteContent, setNoteContent] = useState("");
  const [selectedStudentId, setSelectedStudentId] = useState(initialStudentId || "");
  const [selectedClassId, setSelectedClassId] = useState(initialClassId || "");
  const [visibility, setVisibility] = useState("all_staff");
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const currentUser = getCurrentUser();
  
  // Reset form when dialog opens or closes
  const resetForm = () => {
    setNoteType("general");
    setNoteContent("");
    setSelectedStudentId(initialStudentId || "");
    setSelectedClassId(initialClassId || "");
    setVisibility("all_staff");
  };
  
  // Fetch students data
  const { data: students = [], isLoading: isLoadingStudents } = useQuery({
    queryKey: ["students"],
    queryFn: fetchStudents,
    enabled: open,
  });
  
  // Fetch classes data
  const { data: classesResponse, isLoading: isLoadingClasses } = useQuery({
    queryKey: ["classes"],
    queryFn: fetchClasses,
    enabled: open,
  });

  // Extract classes array from the response
  const classes = classesResponse?.data || [];
  
  const handleCreateNote = async () => {
    // Validate form
    if (!noteContent.trim()) {
      toast.error("Please enter content for the note");
      return;
    }

    // Validate that at least one entity is selected
    if (!selectedStudentId && !selectedClassId) {
      toast.error("Please select either a student or a class for this note");
      return;
    }

    try {
      setIsSubmitting(true);

      // Create note object matching backend API
      const noteData = {
        content: noteContent.trim(),
        type: noteType,
        visibility: visibility,
        studentId: selectedStudentId || undefined,
        classId: selectedClassId || undefined,
        tags: [] // Empty tags array for now
      };

      // Submit note
      await createNote(noteData);

      // Reset form and close dialog
      resetForm();
      onNoteCreated();
      toast.success("Note created successfully");
    } catch (error) {
      console.error("Error creating note:", error);
      toast.error("Failed to create note. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={(newOpen) => {
      if (!newOpen) resetForm();
      onOpenChange(newOpen);
    }}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Create New Note</DialogTitle>
          <DialogDescription>
            Add a new note for a student, class, or general information.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="note-type">Note Type</Label>
              <Select value={noteType} onValueChange={setNoteType}>
                <SelectTrigger id="note-type">
                  <SelectValue placeholder="Select note type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="general">General</SelectItem>
                  <SelectItem value="academic">Academic</SelectItem>
                  <SelectItem value="behavioral">Behavioral</SelectItem>
                  <SelectItem value="attendance">Attendance</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="visibility">Visibility</Label>
              <Select value={visibility} onValueChange={setVisibility}>
                <SelectTrigger id="visibility">
                  <SelectValue placeholder="Select visibility" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="teacher_only">Teacher Only</SelectItem>
                  <SelectItem value="all_staff">All Staff</SelectItem>
                  <SelectItem value="manager_only">Manager Only</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          

          
          <Tabs defaultValue={initialStudentId ? "student" : initialClassId ? "class" : "student"}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="student">Student Note</TabsTrigger>
              <TabsTrigger value="class">Class Note</TabsTrigger>
            </TabsList>
            
            <TabsContent value="student" className="space-y-4 pt-4">
              <div className="space-y-2">
                <Label htmlFor="student">Student</Label>
                <Select 
                  value={selectedStudentId} 
                  onValueChange={setSelectedStudentId}
                  disabled={isLoadingStudents}
                >
                  <SelectTrigger id="student">
                    <SelectValue placeholder="Select a student" />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingStudents ? (
                      <SelectItem value="loading" disabled>Loading students...</SelectItem>
                    ) : students.length === 0 ? (
                      <SelectItem value="none" disabled>No students available</SelectItem>
                    ) : (
                      students.map((student) => (
                        <SelectItem key={student.id} value={student.id}>
                          {student.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>
            
            <TabsContent value="class" className="space-y-4 pt-4">
              <div className="space-y-2">
                <Label htmlFor="class">Class</Label>
                <Select 
                  value={selectedClassId} 
                  onValueChange={setSelectedClassId}
                  disabled={isLoadingClasses}
                >
                  <SelectTrigger id="class">
                    <SelectValue placeholder="Select a class" />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingClasses ? (
                      <SelectItem value="loading" disabled>Loading classes...</SelectItem>
                    ) : classes.length === 0 ? (
                      <SelectItem value="none" disabled>No classes available</SelectItem>
                    ) : (
                      classes.map((classItem) => (
                        <SelectItem key={classItem.id} value={classItem.id}>
                          {classItem.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>
          </Tabs>
          
          <div className="space-y-2">
            <Label htmlFor="content">Content</Label>
            <Textarea
              id="content"
              placeholder="Enter note content"
              className="min-h-[150px]"
              value={noteContent}
              onChange={(e) => setNoteContent(e.target.value)}
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleCreateNote} disabled={isSubmitting}>
            {isSubmitting ? "Creating..." : "Create Note"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CreateNoteDialog;
