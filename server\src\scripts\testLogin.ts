// server/src/scripts/testLogin.ts
import mongoose from 'mongoose';
import { User } from '../models/user.model';
import { AuthService } from '../services/auth.service';
import { DB_CONFIG } from '../config/database.config';

async function testLogin() {
    try {
        // Connect to database
        await mongoose.connect(DB_CONFIG.URI);
        console.log('Connected to database');

        // Find the admin user
        const adminUser = await User.findOne({ username: 'admin@local' });
        
        if (!adminUser) {
            console.log('❌ Admin user not found!');
            return;
        }

        console.log('✅ Admin user found:');
        console.log('Username:', adminUser.username);
        console.log('Role:', adminUser.role);
        console.log('Status:', adminUser.status);
        console.log('Stored password hash:', adminUser.password);

        // Test password comparison
        const testPassword = 'admin';
        console.log('\n🔍 Testing password comparison...');
        console.log('Testing password:', testPassword);
        
        const isValid = await AuthService.comparePasswords(testPassword, adminUser.password);
        console.log('Password comparison result:', isValid);

        if (isValid) {
            console.log('✅ Password comparison successful!');
        } else {
            console.log('❌ Password comparison failed!');
            
            // Let's try to create a new hash and compare
            console.log('\n🔧 Creating new hash for comparison...');
            const newHash = await AuthService.hashPassword(testPassword);
            console.log('New hash:', newHash);
            
            const newComparison = await AuthService.comparePasswords(testPassword, newHash);
            console.log('New hash comparison:', newComparison);
        }

    } catch (error) {
        console.error('Error testing login:', error);
    } finally {
        await mongoose.disconnect();
        console.log('Disconnected from database');
    }
}

// Run the test
testLogin();
