
import { apiClient } from '@/lib/api';

// Fetch notes summary for dashboard
export const fetchNotesSummary = async () => {
  try {
    const response = await apiClient.get('/notes', {
      params: {
        limit: 10,
        page: 1,
        sortBy: 'createdAt',
        sortOrder: 'desc'
      }
    });

    if (!response.data.success) {
      throw new Error('Failed to fetch notes summary');
    }

    const notes = response.data.data || [];
    const pagination = response.data.pagination || { total: 0 };

    // Process notes to create summary structure
    const studentNotes = notes.filter((note: any) => note.studentId);
    const classNotes = notes.filter((note: any) => note.classId && !note.studentId);

    // Create student statistics
    const studentStats = studentNotes.reduce((acc: any, note: any) => {
      if (note.student) {
        const studentId = note.student._id || note.student.id;
        const studentName = note.student.name;
        if (!acc[studentId]) {
          acc[studentId] = { id: studentId, name: studentName, noteCount: 0 };
        }
        acc[studentId].noteCount++;
      }
      return acc;
    }, {});

    // Create class statistics
    const classStats = classNotes.reduce((acc: any, note: any) => {
      if (note.class) {
        const classId = note.class._id || note.class.id;
        const className = note.class.name;
        if (!acc[classId]) {
          acc[classId] = { id: classId, name: className, noteCount: 0 };
        }
        acc[classId].noteCount++;
      }
      return acc;
    }, {});

    return {
      totalNotes: pagination.total,
      recentNotes: {
        student: studentNotes.slice(0, 5),
        class: classNotes.slice(0, 5)
      },
      topStudents: Object.values(studentStats).slice(0, 5),
      topClasses: Object.values(classStats).slice(0, 5)
    };
  } catch (error) {
    console.error('Error fetching notes summary:', error);
    return {
      totalNotes: 0,
      recentNotes: { student: [], class: [] },
      topStudents: [],
      topClasses: []
    };
  }
};

// Fetch all notes with filters
export const fetchNotes = async (filters = {}) => {
  try {
    const response = await apiClient.get('/notes', { params: filters });

    if (!response.data.success) {
      throw new Error('Failed to fetch notes');
    }

    return {
      notes: response.data.data || [],
      pagination: response.data.pagination || {
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0
      }
    };
  } catch (error) {
    console.error('Error fetching notes:', error);
    return {
      notes: [],
      pagination: { total: 0, page: 1, limit: 10, totalPages: 0 }
    };
  }
};

// Fetch student-specific notes
export const fetchStudentNotes = async (studentId: string, filters = {}) => {
  try {
    const response = await apiClient.get(`/notes/students/${studentId}`, { params: filters });

    if (!response.data.success) {
      throw new Error('Failed to fetch student notes');
    }

    return {
      notes: response.data.data || [],
      pagination: response.data.pagination || {
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0
      }
    };
  } catch (error) {
    console.error('Error fetching student notes:', error);
    return {
      notes: [],
      pagination: { total: 0, page: 1, limit: 10, totalPages: 0 }
    };
  }
};

// Fetch class-specific notes
export const fetchClassNotes = async (classId: string, filters = {}) => {
  try {
    const response = await apiClient.get(`/notes/classes/${classId}`, { params: filters });

    if (!response.data.success) {
      throw new Error('Failed to fetch class notes');
    }

    return {
      notes: response.data.data || [],
      pagination: response.data.pagination || {
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0
      }
    };
  } catch (error) {
    console.error('Error fetching class notes:', error);
    return {
      notes: [],
      pagination: { total: 0, page: 1, limit: 10, totalPages: 0 }
    };
  }
};

// Create a new note
export const createNote = async (noteData: any) => {
  try {
    const response = await apiClient.post('/notes', noteData);

    if (!response.data.success) {
      throw new Error('Failed to create note');
    }

    return response.data.data;
  } catch (error) {
    console.error('Error creating note:', error);
    throw error;
  }
};

// Update an existing note
export const updateNote = async (noteId: string, updateData: any) => {
  try {
    const response = await apiClient.patch(`/notes/${noteId}`, updateData);

    if (!response.data.success) {
      throw new Error('Failed to update note');
    }

    return response.data.data;
  } catch (error) {
    console.error('Error updating note:', error);
    throw error;
  }
};

// Delete a note
export const deleteNote = async (noteId: string) => {
  try {
    const response = await apiClient.delete(`/notes/${noteId}`);

    if (!response.data.success) {
      throw new Error('Failed to delete note');
    }

    return response.data;
  } catch (error) {
    console.error('Error deleting note:', error);
    throw error;
  }
};

