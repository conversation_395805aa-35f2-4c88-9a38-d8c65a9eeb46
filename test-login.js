// Simple script to test login functionality
const fetch = require('node-fetch');

async function testLogin() {
    console.log('🧪 Testing Login Functionality...\n');
    
    try {
        // Test 1: Health check
        console.log('1. Testing backend health...');
        const healthResponse = await fetch('http://localhost:3000/health');
        if (healthResponse.ok) {
            const healthData = await healthResponse.json();
            console.log('✅ Backend is healthy:', healthData.status);
        } else {
            console.log('❌ Backend health check failed');
            return;
        }
        
        // Test 2: Login attempt
        console.log('\n2. Testing login with admin@local/admin...');
        const loginResponse = await fetch('http://localhost:3000/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                username: 'admin@local',
                password: 'admin'
            }),
        });
        
        console.log('Login response status:', loginResponse.status);
        
        if (loginResponse.ok) {
            const loginData = await loginResponse.json();
            console.log('✅ Login successful!');
            console.log('User data:', {
                id: loginData.user?.id,
                username: loginData.user?.username,
                role: loginData.user?.role
            });
            console.log('Token received:', !!loginData.token);
        } else {
            const errorData = await loginResponse.text();
            console.log('❌ Login failed:', errorData);
        }
        
    } catch (error) {
        console.error('❌ Test failed with error:', error.message);
        console.log('\nPossible issues:');
        console.log('- Backend server is not running');
        console.log('- MongoDB is not running');
        console.log('- Database has not been seeded');
        console.log('\nTry running: setup-database.bat');
    }
}

// Run the test
testLogin();
