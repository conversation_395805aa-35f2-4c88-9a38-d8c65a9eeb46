
import MainLayout from "@/components/layout/MainLayout";
import TeacherScheduling from "@/components/teacher-scheduling/TeacherScheduling";
import { hasRole } from "@/lib/auth";
import { Navigate, useParams } from "react-router-dom";

const TeacherSchedulingPage = () => {
  const { id } = useParams<{ id: string }>();
  
  // Check if user has proper permissions
  const canManageTeachers = hasRole(['superAdmin', 'manager']);

  if (!canManageTeachers) {
    return <Navigate to="/classes" replace />;
  }

  return (
    <MainLayout>
      <TeacherScheduling classId={id} />
    </MainLayout>
  );
};

export default TeacherSchedulingPage;
