# Login Issue Troubleshooting Guide

## Problem: Cannot login with admin@local / admin

### Root Causes & Solutions

## 1. **MongoDB Not Running** (Most Common)
**Symptoms:** Backend shows connection errors, login fails
**Solution:**
```bash
# Run the database setup script
setup-database.bat
```

## 2. **Database Not Seeded**
**Symptoms:** <PERSON><PERSON> fails with "Invalid credentials"
**Solution:**
```bash
# Seed the database manually
cd server
npm run seed
```

## 3. **Backend Server Not Running**
**Symptoms:** Network errors, connection refused
**Solution:**
```bash
# Start backend server
cd server
npm run dev
```

## 4. **Field Mismatch Fixed**
**Issue:** <PERSON><PERSON> was sending `email` but backend expects `username`
**Status:** ✅ FIXED - LoginForm now correctly sends username field

## 5. **Pre-filled Credentials**
**Status:** ✅ FIXED - Login form now pre-fills with admin@local/admin for testing

## Step-by-Step Troubleshooting

### Step 1: Check MongoDB
```bash
# Windows
sc query "MongoDB"

# If not running
net start MongoDB
```

### Step 2: Seed Database
```bash
cd server
npm run seed
```
Expected output:
```
✅ Created superAdmin: admin@local/admin
✅ Created manager: manager@local/manager
✅ Created secretary: secretary@local/secretary
✅ Created teacher: teacher@local/teacher
```

### Step 3: Start Backend
```bash
cd server
npm run dev
```
Should show:
```
Connected to MongoDB successfully
Server is running on port 3000
```

### Step 4: Start Frontend
```bash
cd client
npm run dev
```

### Step 5: Test Login
1. Go to http://localhost:8080
2. Use credentials: admin@local / admin
3. Should redirect to dashboard

## Quick Test Script
```bash
# Test if everything works
node test-login.js
```

## Manual Database Check
If you have MongoDB Compass or mongo shell:
```javascript
// Connect to vertex database
use vertex

// Check if users exist
db.users.find({}, {username: 1, role: 1})

// Should show:
// { username: "admin@local", role: "superAdmin" }
// { username: "manager@local", role: "manager" }
// etc.
```

## Alternative: MongoDB Atlas
If local MongoDB doesn't work:
1. Create free account at https://www.mongodb.com/atlas
2. Get connection string
3. Update `server/.env`:
   ```
   MONGODB_URI=mongodb+srv://username:<EMAIL>/vertex
   ```
4. Run seed script again

## Common Error Messages

### "Invalid credentials"
- Database not seeded
- Wrong username/password
- User doesn't exist

### "Connection refused"
- Backend not running
- Wrong port (should be 3000)

### "MongoDB connection error"
- MongoDB not installed/running
- Wrong connection string
- Network issues

### "Token expired"
- Old session data
- Clear browser localStorage
- Refresh page

## Verification Checklist
- [ ] MongoDB is running
- [ ] Database is seeded with users
- [ ] Backend server is running on port 3000
- [ ] Frontend is running on port 8080
- [ ] No console errors in browser
- [ ] Network tab shows successful API calls

## Success Indicators
✅ Login form pre-filled with admin@local/admin
✅ No console errors
✅ Successful redirect to dashboard
✅ User data displayed correctly
