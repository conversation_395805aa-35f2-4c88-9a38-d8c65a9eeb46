import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
    hmr: {
      overlay: false, // Disable error overlay that might cause refreshes
      port: 24678, // Use a specific port for HMR
    },
    // Prevent excessive file watching that can cause refresh loops
    watch: {
      usePolling: false,
      interval: 1000,
    },
  },
  plugins: [
    react({
      // Optimize React refresh to prevent excessive re-renders
      fastRefresh: true,
    }),
    // Disable componentTagger completely to prevent refresh issues
    // mode === 'development' && componentTagger(),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  // Optimize dependencies to prevent unnecessary reloads
  optimizeDeps: {
    include: ['react', 'react-dom', '@tanstack/react-query'],
  },
}));
