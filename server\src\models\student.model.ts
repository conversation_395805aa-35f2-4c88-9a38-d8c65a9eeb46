// server/src/models/student.model.ts
import mongoose, { Schema, Types, Document, Model } from 'mongoose';
import { ClassHistoryItem, IStudent, PaymentSummaryDTO, StudentArchiveData } from '../types/student.types';

// Define base interface without _id to avoid conflict
interface IStudentBase extends Omit<IStudent, '_id'> {
  calculatePaymentSummary(): PaymentSummaryDTO;
  transferClass(newClassId: Types.ObjectId, reason: string, transferDate?: Date): Promise<Document & IStudentBase>;
}

// Combine with Document type
interface IStudentDocument extends Document, IStudentBase {}

// Define model interface
interface IStudentModel extends Model<IStudentDocument> {
  archiveStudent(studentId: Types.ObjectId, archivedBy: Types.ObjectId): Promise<StudentArchiveData>;
  restoreStudent(studentId: Types.ObjectId): Promise<IStudentDocument>;
}

const studentSchema = new Schema<IStudentDocument, IStudentModel>(
  {
    name: {
      type: String,
      required: true,
      trim: true
      // Removed index: true to avoid duplicate with schema.index() below
    },
    contactInfo: {
      phone: {
        type: String,
        required: true
      },
      email: {
        type: String,
        required: true,
        lowercase: true,
        unique: true
        // Removed index: true to avoid duplicate
      },
      address: {
        type: String,
        required: true
      }
    },
    status: {
      type: String,
      required: true,
      enum: ['active', 'inactive'],
      default: 'active'
      // Removed index: true to avoid duplicate
    },
    currentLevel: {
      type: String,
      required: true
      // Removed index: true to avoid duplicate
    },
    currentClass: {
      type: Schema.Types.ObjectId,
      ref: 'Class'
      // Removed index: true to avoid duplicate
    },
    registeredAt: {
      type: Date,
      default: Date.now,
      required: true
    },
    registeredBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    payments: [{
      amount: {
        type: Number,
        required: true
      },
      date: {
        type: Date,
        required: true
      },
      recordedBy: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      remainingBalance: {
        type: Number,
        required: true
      },
      nextDueDate: {
        type: Date,
        required: true
        // Removed index: true to avoid duplicate
      },
      notes: String
    }],
    levelHistory: [{
      fromLevel: {
        type: String,
        required: true
      },
      toLevel: {
        type: String,
        required: true
      },
      date: {
        type: Date,
        required: true
      },
      reason: {
        type: String,
        required: true
      },
      approvedBy: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
      }
    }],
    classHistory: [{
      classId: {
        type: Schema.Types.ObjectId,
        ref: 'Class',
        required: true
      },
      startDate: {
        type: Date,
        required: true
      },
      endDate: Date,
      reason: String
    }]
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
  }
);

// Indexes
studentSchema.index({ name: 'text', 'contactInfo.email': 'text', 'contactInfo.phone': 'text' });
studentSchema.index({ currentClass: 1, status: 1 });
studentSchema.index({ currentLevel: 1, status: 1 });
studentSchema.index({ 'payments.nextDueDate': 1, status: 1 });

// Virtual for calculating total payments
studentSchema.virtual('totalPayments').get(function(this: IStudentDocument) {
  return this.payments.reduce((sum: number, payment: { amount: number }) => sum + payment.amount, 0);
});

// Virtual for getting latest payment
studentSchema.virtual('latestPayment').get(function(this: IStudentDocument) {
  if (!this.payments.length) return null;
  return this.payments.sort((a, b) => b.date.getTime() - a.date.getTime())[0];
});

// Pre-save middleware
studentSchema.pre('save', async function(this: IStudentDocument, next) {
  // If student is being deactivated, ensure no active class assignments
  if (this.isModified('status') && this.status === 'inactive') {
    if (this.currentClass) {
      throw new Error('Cannot deactivate student with active class assignment');
    }
  }

  // If level is changed, add to level history
  if (this.isModified('currentLevel')) {
    const oldLevel = this.modifiedPaths().includes('currentLevel')
      ? this.get('currentLevel', String, { getters: false })
      : this.currentLevel;

    if (oldLevel && oldLevel !== this.currentLevel) {
      this.levelHistory.push({
        fromLevel: oldLevel,
        toLevel: this.currentLevel,
        date: new Date(),
        reason: 'Level Update',
        approvedBy: this.get('_modifiedBy') as Types.ObjectId
      });
    }
  }

  next();
});

// Methods for payment calculations
studentSchema.methods.calculatePaymentSummary = function(this: IStudentDocument): PaymentSummaryDTO {
  const latestPaymentDoc = this.get('latestPayment');
  const totalPaid = this.payments.reduce(
    (sum: number, payment: { amount: number }) => sum + payment.amount,
    0
  );
  const now = new Date();

  const summary: PaymentSummaryDTO = {
    totalPaid,
    totalDue: latestPaymentDoc ? latestPaymentDoc.remainingBalance : 0,
    lastPaymentDate: latestPaymentDoc ? latestPaymentDoc.date : undefined,
    nextDueDate: latestPaymentDoc ? latestPaymentDoc.nextDueDate : undefined,
    paymentStatus: 'paid'
  };

  if (latestPaymentDoc) {
    if (latestPaymentDoc.remainingBalance > 0) {
      summary.paymentStatus = latestPaymentDoc.nextDueDate < now ? 'overdue' : 'pending';
    }
  }

  return summary;
};

// Methods for class operations
studentSchema.methods.transferClass = async function(
    this: IStudentDocument,
    newClassId: Types.ObjectId,
    reason: string,
    transferDate: Date = new Date()
  ): Promise<IStudentDocument> {
    if (this.currentClass) {
      // End current class assignment
      const currentAssignment = this.classHistory.find(
        h => h.classId.equals(this.currentClass) && !h.endDate
      );
      if (currentAssignment) {
        currentAssignment.endDate = transferDate;
        currentAssignment.reason = reason;
      }
    }
  
    // Create new class history entry
    const newClassHistory: ClassHistoryItem = {
      classId: newClassId,
      startDate: transferDate,
      reason: reason
    };
  
    // Add to class history
    this.classHistory = [...this.classHistory, newClassHistory];
  
    // Update current class
    this.currentClass = newClassId;
    return this.save();
  };

// Static methods
studentSchema.static('archiveStudent', async function(
    studentId: Types.ObjectId,
    archivedBy: Types.ObjectId
  ): Promise<StudentArchiveData> {
    const student = await this.findById(studentId).exec();
    if (!student) {
      throw new Error('Student not found');
    }
  
    const archiveData: StudentArchiveData = {
      _id: new Types.ObjectId(),
      studentId: student._id as Types.ObjectId,  // Explicit type assertion
      name: student.name,
      status: student.status,
      archivedAt: new Date(),
      archivedBy,
      lastLevel: student.currentLevel,
      lastClass: student.currentClass,
      paymentHistory: student.payments.map((p: { amount: number; date: Date; recordedBy: Types.ObjectId }) => ({
        amount: p.amount,
        date: p.date,
        recordedBy: p.recordedBy
      })),
      classHistory: student.classHistory
    };
  
    await mongoose.connection.collection('student_archives').insertOne(archiveData);
    return archiveData;
  });

studentSchema.static('restoreStudent', async function(
  studentId: Types.ObjectId
): Promise<IStudentDocument> {
  const archive = await mongoose.connection
    .collection('student_archives')
    .findOne({ studentId });

  if (!archive) {
    throw new Error('Archive not found');
  }

  const restoredStudent = new this({
    _id: studentId,
    name: archive.name,
    status: 'inactive',
    currentLevel: archive.lastLevel,
    registeredAt: archive.registeredAt,
    classHistory: archive.classHistory
  });

  return restoredStudent.save();
});

// Add indexes for better query performance
studentSchema.index({ name: 1 });
studentSchema.index({ status: 1 });
studentSchema.index({ currentLevel: 1 });
studentSchema.index({ currentClass: 1 });
studentSchema.index({ registeredAt: -1 });
studentSchema.index({ 'payments.date': -1 });
studentSchema.index({ 'classHistory.classId': 1 });

// Compound indexes for common queries
studentSchema.index({ status: 1, currentLevel: 1 });
studentSchema.index({ currentClass: 1, status: 1 });
studentSchema.index({ registeredAt: -1, status: 1 });

// Additional indexes for payment queries
studentSchema.index({ 'payments.remainingBalance': 1 });
studentSchema.index({ 'payments.nextDueDate': 1 });

export const Student = mongoose.model<IStudentDocument, IStudentModel>('Student', studentSchema);
export { IStudentDocument, IStudentModel, IStudentBase };