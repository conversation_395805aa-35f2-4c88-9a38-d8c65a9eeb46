// server/src/controllers/class.controller.ts
import { Request, Response } from 'express';
import { ClassService } from '../services/class.service';
import { AppError } from '../types/error.types';
import { SystemLogger } from '../services/logger.service';
import {
    ClassQueryOptions,
    CreateClassDTO,
    UpdateClassDTO,
    ScheduleMakeupClassDTO,
    MergeClassDTO,
    ClassBulkOperationDTO,
    ClassExportOptions,
    validClassFields,
    ValidClassField, 
    TeacherReplacementDTO,
    SplitClassDTO,
    TeacherTransitionDTO
} from '../types/class.types';


export class ClassController {
    static async getClasses(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const {
            page,
            limit,
            sortBy,
            sortOrder,
            status,
            level,
            teacherId,
            search,
            room,
            fromDate,
            toDate,
            hasCapacity
        } = req.query;

        const options: ClassQueryOptions = {
            page: page ? parseInt(page as string) : undefined,
            limit: limit ? parseInt(limit as string) : undefined,
            sortBy: sortBy as 'name' | 'level' | 'startDate' | 'endDate' | 'currentStudentCount' | 'status' | undefined, //it was originally sortBy: sortBy as keyof ClassQueryOptions,
            sortOrder: sortOrder as 'asc' | 'desc',
            status: status as 'active' | 'inactive' | 'merged',
            level: level as string,
            teacherId: teacherId as string,
            search: search as string,
            room: room as string,
            fromDate: fromDate ? new Date(fromDate as string) : undefined,
            toDate: toDate ? new Date(toDate as string) : undefined,
            hasCapacity: hasCapacity === 'true'
        };

        const result = await ClassService.getClasses(options, currentUser._id.toString());

        res.json({
            success: true,
            data: result.classes,
            pagination: result.pagination
        });
    }

    static async getClassById(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const classDetails = await ClassService.getClassById(id, currentUser._id.toString());

        res.json({
            success: true,
            data: classDetails
        });
    }

    static async createClass(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const classData: CreateClassDTO = req.body;
        const newClass = await ClassService.createClass(
            classData,
            currentUser._id.toString()
        );

        await SystemLogger.log({
            severity: 'info',
            category: 'class_management',
            action: 'create_class',
            performedBy: currentUser._id.toString(),
            details: {
                classId: newClass.id,
                className: newClass.name,
                level: newClass.level
            },
            status: 'success',
            timestamp: new Date()
        });

        res.status(201).json({
            success: true,
            message: 'Class created successfully',
            data: newClass
        });
    }

    static async updateClass(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const updateData: UpdateClassDTO = req.body;

        const updatedClass = await ClassService.updateClass(
            id,
            updateData,
            currentUser._id.toString()
        );

        await SystemLogger.log({
            severity: 'info',
            category: 'class_management',
            action: 'update_class',
            performedBy: currentUser._id.toString(),
            targetId: id,
            details: {
                className: updatedClass.name,
                updates: updateData
            },
            status: 'success',
            timestamp: new Date()
        });

        res.json({
            success: true,
            message: 'Class updated successfully',
            data: updatedClass
        });
    }

    static async scheduleMakeupClass(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const makeupData: ScheduleMakeupClassDTO = req.body;

        const result = await ClassService.scheduleMakeupClass(
            id,
            makeupData,
            currentUser._id.toString()
        );

        await SystemLogger.log({
            severity: 'info',
            category: 'class_management',
            action: 'schedule_makeup',
            performedBy: currentUser._id.toString(),
            targetId: id,
            details: {
                originalDate: makeupData.originalDate,
                makeupDate: makeupData.makeupDate
            },
            status: 'success',
            timestamp: new Date()
        });

        res.json({
            success: true,
            message: 'Makeup class scheduled successfully',
            data: result
        });
    }

    static async mergeClasses(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const mergeData: MergeClassDTO = req.body;

        const result = await ClassService.mergeClasses(
            id,
            mergeData,
            currentUser._id.toString()
        );

        await SystemLogger.log({
            severity: 'info',
            category: 'class_management',
            action: 'merge_classes',
            performedBy: currentUser._id.toString(),
            targetId: id,
            details: {
                targetClassId: mergeData.targetClassId,
                reason: mergeData.reason
            },
            status: 'success',
            timestamp: new Date()
        });

        res.json({
            success: true,
            message: 'Classes merged successfully',
            data: result
        });
    }

    static async bulkOperation(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const operationData: ClassBulkOperationDTO = req.body;
        const result = await ClassService.bulkOperation(
            operationData,
            currentUser._id.toString()
        );

        await SystemLogger.log({
            severity: 'info',
            category: 'class_management',
            action: 'bulk_operation',
            performedBy: currentUser._id.toString(),
            details: {
                operation: operationData.operation,
                classCount: operationData.classIds.length,
                summary: result
            },
            status: 'success',
            timestamp: new Date()
        });

        res.json({
            success: true,
            message: 'Bulk operation completed',
            data: result
        });
    }

    static async exportClasses(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const {
            format = 'csv',
            fields,
            includeStudentHistory,
            includeMakeupClasses,
            fromDate,
            toDate
        } = req.query;

        const validFields = ['name', 'level', 'room', 'capacity', 'status', 'startDate', 'endDate'] as const;
        type ValidField = typeof validFields[number];

        const options: ClassExportOptions = {
            format: format as 'csv' | 'json',
            fields: fields ? 
              (fields as string).split(',').filter((field): field is ValidClassField => 
                validClassFields.includes(field as ValidClassField)
              ) : 
              undefined,
            includeStudentHistory: includeStudentHistory === 'true',
            includeMakeupClasses: includeMakeupClasses === 'true',
            dateRange: fromDate && toDate ? {
              start: new Date(fromDate as string),
              end: new Date(toDate as string)
            } : undefined
          };

        const exportData = await ClassService.exportClasses(
            options,
            currentUser._id.toString()
        );

        const filename = `classes_export_${new Date().toISOString()}.${format}`;
        res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
        res.setHeader('Content-Type', format === 'csv' ? 'text/csv' : 'application/json');
        res.send(exportData);
    }

    static async getClassSchedule(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const { includeMakeup = 'false' } = req.query;

        const schedule = await ClassService.getClassSchedule(
            id,
            includeMakeup === 'true',
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: schedule
        });
    }

    static async getRoomAvailability(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { room } = req.params;
        const { date } = req.query;

        const availability = await ClassService.getRoomAvailability(
            room,
            date ? new Date(date as string) : new Date(),
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: availability
        });
    }

    static async getTeacherSchedule(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { teacherId } = req.params;
        const { fromDate, toDate } = req.query;

        const schedule = await ClassService.getTeacherSchedule(
            teacherId,
            {
                fromDate: fromDate ? new Date(fromDate as string) : undefined,
                toDate: toDate ? new Date(toDate as string) : undefined
            },
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: schedule
        });
    }

    static async getClassStatistics(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { fromDate, toDate, level } = req.query;

        const statistics = await ClassService.getClassStatistics(
            {
                fromDate: fromDate ? new Date(fromDate as string) : undefined,
                toDate: toDate ? new Date(toDate as string) : undefined,
                level: level as string
            },
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: statistics
        });
    }

    static async getAvailableLevels(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const levels = await ClassService.getAvailableLevels(
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: levels
        });
    }

    static async getAvailableRooms(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const rooms = await ClassService.getAvailableRooms(
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: rooms
        });
    }

    static async replaceTeacher(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
          throw new AppError(401, 'User not authenticated');
        }
      
        const { id } = req.params;
        const replacementData: TeacherReplacementDTO = req.body;
      
        const result = await ClassService.replaceTeacher(
          id,
          replacementData,
          currentUser._id.toString()
        );
      
        await SystemLogger.log({
          severity: 'info',
          category: 'class_management',
          action: 'teacher_replacement',
          performedBy: currentUser._id.toString(),
          targetId: id,
          details: {
            originalTeacherId: replacementData.originalTeacherId,
            newTeacherId: replacementData.newTeacherId,
            effectiveDate: replacementData.effectiveDate
          },
          status: 'success',
          timestamp: new Date()
        });
      
        res.json({
          success: true,
          message: 'Teacher replaced successfully',
          data: result
        });
    }

    static async splitClass(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }
    
        const { id } = req.params;
        const splitData: SplitClassDTO = req.body;
    
        const result = await ClassService.splitClass(
            id,
            splitData,
            currentUser._id.toString()
        );
    
        await SystemLogger.log({
            severity: 'info',
            category: 'class_management',
            action: 'split_class',
            performedBy: currentUser._id.toString(),
            targetId: id,
            details: {
                newClassId: result.newClass.id,
                reason: splitData.reason
            },
            status: 'success',
            timestamp: new Date()
        });
    
        res.json({
            success: true,
            message: 'Class split successfully',
            data: result
        });
    }

    static async initiateTeacherTransition(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }
    
        const { id } = req.params;
        const transitionData: TeacherTransitionDTO = req.body;
    
        const result = await ClassService.initiateTeacherTransition(
            id,
            transitionData,
            currentUser._id.toString()
        );
    
        res.json({
            success: true,
            message: 'Teacher transition initiated successfully',
            data: result
        });
    }

}