// server/src/controllers/auth.controller.ts
import { Request, Response } from 'express';
import { User } from '../models/user.model';
import { AuthService } from '../services/auth.service';
import { SystemLogger } from '../services/logger.service';
import { AppError } from '../types/error.types';

export class AuthController {
  static async login(req: Request, res: Response) {
    const { username, password } = req.body;
    const user = await User.findOne({ username });

    if (!user) {
      throw new AppError(401, 'Invalid credentials');
    }

    if (user.status === 'inactive') {
      throw new AppError(403, 'Account is inactive');
    }

    // Check login attempts
    const requestIp = req.ip || req.connection.remoteAddress || 'unknown';
    if (!await AuthService.checkLoginAttempts(user, requestIp)) {
      throw new AppError(429, 'Too many failed login attempts. Please try again later.');
    }

    // Verify password
    const isValidPassword = await AuthService.comparePasswords(password, user.password);
    
    // Record login attempt
    user.loginAttempts.push({
      timestamp: new Date(),
      ipAddress: requestIp,
      success: isValidPassword
    });

    if (!isValidPassword) {
      await user.save();
      throw new AppError(401, 'Invalid credentials');
    }

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Generate token
    const token = AuthService.generateToken(user._id.toString());

    // Log successful login
    await SystemLogger.log({
      severity: 'info',
      category: 'security',
      action: 'login',
      performedBy: user._id.toString(),
      details: {
        username: username,
        ipAddress: requestIp
      },
      timestamp: new Date(),
      status: 'success'
    });

    return res.json({
      success: true,
      token,
      user: {
        id: user._id,
        _id: user._id,
        name: user.username,
        username: user.username,
        email: user.username,
        role: user.role
      }
    });
  }

  static async createUser(req: Request, res: Response) {
    const { username, password, role } = req.body;
    const currentUser = req.user;

    if (!currentUser?._id) {
      throw new AppError(401, 'User not authenticated');
    }

    // Check if username already exists
    const existingUser = await User.findOne({ username });
    if (existingUser) {
      throw new AppError(400, 'Username already exists');
    }

    // If trying to create another SuperAdmin, ensure at least one will remain active
    if (role === 'superAdmin') {
      const activeSuperAdmins = await User.countDocuments({ 
        role: 'superAdmin',
        status: 'active'
      });
      
      if (activeSuperAdmins < 1) {
        throw new AppError(400, 'At least one active SuperAdmin must exist');
      }
    }

    // Hash password
    const hashedPassword = await AuthService.hashPassword(password);

    // Create new user
    const newUser = new User({
      username,
      password: hashedPassword,
      role,
      status: 'active',
      createdBy: currentUser._id.toString(),
      roleHistory: [{
        role,
        changedAt: new Date(),
        changedBy: currentUser._id.toString(),
        reason: 'Initial role assignment'
      }]
    });

    await newUser.save();

    // Log user creation
    await SystemLogger.log({
      severity: 'info',
      category: 'security',
      action: 'user_creation',
      performedBy: currentUser._id.toString(),
      details: { 
        createdUsername: username,
        role: role
      },
      timestamp: new Date(),
      status: 'success'
    });

    return res.status(201).json({
      message: 'User created successfully',
      user: {
        id: newUser._id,
        username: newUser.username,
        role: newUser.role
      }
    });
  }

  static async resetPassword(req: Request, res: Response) {
    const { username } = req.body;
    const currentUser = req.user;

    if (!currentUser?._id) {
      throw new AppError(401, 'User not authenticated');
    }

    const user = await User.findOne({ username });
    if (!user) {
      throw new AppError(404, 'User not found');
    }

    // Generate a secure random password
    const newPassword = AuthService.generateSecurePassword();
    const hashedPassword = await AuthService.hashPassword(newPassword);

    user.password = hashedPassword;
    user.modifiedAt = new Date();
    await user.save();

    // Log password reset
    await SystemLogger.log({
      severity: 'info',
      category: 'security',
      action: 'password_reset',
      performedBy: currentUser._id.toString(),
      details: { targetUser: username },
      timestamp: new Date(),
      status: 'success'
    });

    return res.json({
      message: 'Password reset successful',
      newPassword // In a production environment, this should be sent via a secure channel
    });
  }

  static async changePassword(req: Request, res: Response) {
    const { currentPassword, newPassword } = req.body;
    const currentUser = req.user;

    if (!currentUser?._id) {
      throw new AppError(401, 'User not authenticated');
    }

    const user = await User.findById(currentUser._id);
    if (!user) {
      throw new AppError(404, 'User not found');
    }

    // Verify current password
    const isValidPassword = await AuthService.comparePasswords(currentPassword, user.password);
    if (!isValidPassword) {
      throw new AppError(401, 'Current password is incorrect');
    }

    // Hash and save new password
    const hashedPassword = await AuthService.hashPassword(newPassword);
    user.password = hashedPassword;
    user.modifiedAt = new Date();
    await user.save();

    // Log password change
    await SystemLogger.log({
      severity: 'info',
      category: 'security',
      action: 'password_change',
      performedBy: currentUser._id.toString(),
      details: { 
        username: user.username
      },
      timestamp: new Date(),
      status: 'success'
    });

    return res.json({
      message: 'Password changed successfully'
    });
  }

  static async updateRole(req: Request, res: Response) {
    const { role, reason } = req.body;
    const { id } = req.params;
    const currentUser = req.user;

    if (!currentUser?._id) {
      throw new AppError(401, 'User not authenticated');
    }

    const user = await User.findById(id);
    if (!user) {
      throw new AppError(404, 'User not found');
    }

    // Prevent self-role update
    if (user._id.toString() === currentUser._id.toString()) {
      throw new AppError(400, 'Cannot update own role');
    }

    // If changing from SuperAdmin, ensure at least one will remain
    if (user.role === 'superAdmin' && role !== 'superAdmin') {
      const activeSuperAdmins = await User.countDocuments({
        role: 'superAdmin',
        status: 'active',
        _id: { $ne: user._id }
      });

      if (activeSuperAdmins < 1) {
        throw new AppError(400, 'Cannot change role: At least one SuperAdmin must exist');
      }
    }

    // Update role and role history
    user.role = role;
    user.roleHistory.push({
      role,
      changedAt: new Date(),
      changedBy: currentUser._id,
      reason
    });
    user.modifiedAt = new Date();

    await user.save();

    // Log role update
    await SystemLogger.log({
      severity: 'info',
      category: 'security',
      action: 'role_update',
      performedBy: currentUser._id.toString(),
      details: { 
        targetUser: user.username,
        oldRole: user.role,
        newRole: role,
        reason
      },
      timestamp: new Date(),
      status: 'success'
    });

    return res.json({
      message: 'User role updated successfully',
      user: {
        id: user._id,
        username: user.username,
        role: user.role
      }
    });
  }

  static async getCurrentUser(req: Request, res: Response) {
    const currentUser = req.user;

    if (!currentUser?._id) {
      throw new AppError(401, 'User not authenticated');
    }

    const user = await User.findById(currentUser._id)
      .select('-password -loginAttempts');

    if (!user) {
      throw new AppError(404, 'User not found');
    }

    return res.json({ user });
  }

  static async logout(req: Request, res: Response) {
    const currentUser = req.user;

    if (!currentUser?._id) {
      throw new AppError(401, 'User not authenticated');
    }

    // Log logout
    await SystemLogger.log({
      severity: 'info',
      category: 'security',
      action: 'logout',
      performedBy: currentUser._id.toString(),
      details: { username: currentUser.username },
      status: 'success',
      timestamp: new Date()
    });

    return res.json({
      message: 'Logged out successfully'
    });
  }
}