// server/src/validations/auth.validation.ts
import <PERSON><PERSON> from 'joi';
import { AUTH_CONFIG } from '../config/auth.config';

const passwordPattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;

export const authValidation = {
  login: Joi.object({
    body: Joi.object({
      username: Joi.string().required().messages({
        'string.empty': 'Username is required',
        'any.required': 'Username is required'
      }),
      password: Joi.string().required().messages({
        'string.empty': 'Password is required',
        'any.required': 'Password is required'
      })
    })
  }),

  createUser: Joi.object({
    body: Joi.object({
      username: Joi.string()
        .min(3)
        .max(30)
        .required()
        .pattern(/^[a-zA-Z0-9_]+$/)
        .messages({
          'string.base': 'Username must be a string',
          'string.min': 'Username must be at least 3 characters long',
          'string.max': 'Username cannot exceed 30 characters',
          'string.pattern.base': 'Username can only contain letters, numbers and underscores',
          'any.required': 'Username is required'
        }),
      password: Joi.string()
        .pattern(passwordPattern)
        .required()
        .messages({
          'string.pattern.base':
            'Password must contain at least 8 characters, one uppercase letter, one lowercase letter, one number and one special character',
          'any.required': 'Password is required'
        }),
      role: Joi.string()
        .valid('superAdmin', 'manager', 'secretary', 'teacher')
        .required()
        .messages({
          'any.only': 'Invalid role specified',
          'any.required': 'Role is required'
        })
    })
  }),

  changePassword: Joi.object({
    body: Joi.object({
      currentPassword: Joi.string().required().messages({
        'any.required': 'Current password is required'
      }),
      newPassword: Joi.string()
        .pattern(passwordPattern)
        .required()
        .messages({
          'string.pattern.base':
            'New password must contain at least 8 characters, one uppercase letter, one lowercase letter, one number and one special character',
          'any.required': 'New password is required'
        }),
      confirmPassword: Joi.string()
        .valid(Joi.ref('newPassword'))
        .required()
        .messages({
          'any.only': 'Passwords do not match',
          'any.required': 'Password confirmation is required'
        })
    })
  }),

  resetPassword: Joi.object({
    body: Joi.object({
      username: Joi.string().required().messages({
        'any.required': 'Username is required'
      })
    })
  }),

  updateRole: Joi.object({
    body: Joi.object({
      role: Joi.string()
        .valid('manager', 'secretary', 'teacher')
        .required()
        .messages({
          'any.only': 'Invalid role specified',
          'any.required': 'Role is required'
        }),
      reason: Joi.string().required().min(10).messages({
        'string.min': 'Reason must be at least 10 characters long',
        'any.required': 'Reason for role change is required'
      })
    })
  })
};