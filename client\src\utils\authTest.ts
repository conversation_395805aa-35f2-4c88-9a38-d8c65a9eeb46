// Simple utility to test authentication flow
export const testAuth = () => {
  console.log('=== Authentication Test ===');
  
  // Check if tokens exist
  const jwtToken = localStorage.getItem('jwt_token');
  const authToken = localStorage.getItem('auth_token');
  
  console.log('JWT Token exists:', !!jwtToken);
  console.log('Auth Token exists:', !!authToken);
  
  if (authToken) {
    try {
      const userData = JSON.parse(atob(authToken));
      console.log('User data:', userData);
      console.log('User role:', userData.role);
      console.log('Token expiry:', new Date(userData.exp * 1000));
    } catch (error) {
      console.error('Error parsing auth token:', error);
    }
  }
  
  console.log('=== End Authentication Test ===');
};

// Test API connectivity
export const testAPI = async () => {
  console.log('=== API Test ===');
  
  try {
    // Test health endpoint (no auth required)
    const healthResponse = await fetch('http://localhost:3000/health');
    console.log('Health endpoint status:', healthResponse.status);
    
    if (healthResponse.ok) {
      const healthData = await healthResponse.json();
      console.log('Health data:', healthData);
    }
    
    // Test dashboard endpoint (auth required)
    const token = localStorage.getItem('jwt_token');
    if (token) {
      const dashboardResponse = await fetch('http://localhost:3000/api/dashboard/data', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });
      console.log('Dashboard endpoint status:', dashboardResponse.status);
      
      if (dashboardResponse.ok) {
        const dashboardData = await dashboardResponse.json();
        console.log('Dashboard data received:', !!dashboardData.data);
      } else {
        console.log('Dashboard error:', await dashboardResponse.text());
      }
    } else {
      console.log('No JWT token found for dashboard test');
    }
    
  } catch (error) {
    console.error('API test error:', error);
  }
  
  console.log('=== End API Test ===');
};

// Add to window for easy access in browser console
if (typeof window !== 'undefined') {
  (window as any).testAuth = testAuth;
  (window as any).testAPI = testAPI;
}
