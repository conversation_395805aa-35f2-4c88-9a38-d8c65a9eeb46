
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { createStudent, getSuggestedClasses } from "@/services/studentService";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm, useFieldArray } from "react-hook-form";
import * as z from "zod";
import MainLayout from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { 
  ChevronLeft, 
  User, 
  Phone, 
  Mail, 
  MapPin,
  BookOpen,
  Users,
  Plus,
  Trash
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useQuery } from "@tanstack/react-query";
import { ParentInfo } from "@/types/student";

// Phone regex pattern for validation
const PHONE_REGEX = /^\+?[0-9]{7,15}$/;
// Simple email validation pattern
const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

// Define parent schema
const parentSchema = z.object({
  fullName: z.string().min(2, "Full name must be at least 2 characters"),
  relationship: z.enum(["mother", "father", "guardian", "other"], {
    required_error: "Relationship is required",
  }),
  phone: z.string().regex(PHONE_REGEX, "Invalid phone number format"),
  email: z.string().regex(EMAIL_REGEX, "Invalid email address").optional().or(z.literal('')),
  occupation: z.string().optional(),
  isEmergencyContact: z.boolean().default(false),
});

// Define form schema
const formSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  email: z.string().regex(EMAIL_REGEX, "Invalid email address"),
  phone: z.string().regex(PHONE_REGEX, "Invalid phone number format"),
  address: z.string().min(5, "Address must be at least 5 characters"),
  currentLevel: z.string().min(1, "Level is required"),
  currentClassId: z.string().optional(),
  status: z.enum(["active", "pending", "inactive"], {
    required_error: "Status is required",
  }).default("active"),
  notes: z.string().optional(),
  parents: z.array(parentSchema).optional(),
});

type FormValues = z.infer<typeof formSchema>;

export default function StudentCreate() {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      address: "",
      currentLevel: "",
      status: "active",
      notes: "",
      parents: [
        {
          fullName: "",
          relationship: "mother",
          phone: "",
          email: "",
          occupation: "",
          isEmergencyContact: true,
        }
      ],
    },
  });
  
  // Field array for parents
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "parents",
  });
  
  // Watch currentLevel to fetch suggested classes
  const currentLevel = form.watch("currentLevel");
  
  // Fetch suggested classes based on selected level
  const { data: suggestedClasses, isLoading: isLoadingClasses } = useQuery({
    queryKey: ['suggestedClasses', currentLevel],
    queryFn: () => getSuggestedClasses("new"),
    enabled: !!currentLevel
  });
  
  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    
    try {
      const studentData = {
        name: `${data.firstName} ${data.lastName}`.trim(),
        contactInfo: {
          email: data.email,
          phone: data.phone,
          address: data.address
        },
        parents: data.parents as ParentInfo[],
        status: data.status,
        currentLevel: data.currentLevel,
        currentClass: data.currentClassId && data.currentClassId !== "no-class" ? data.currentClassId : undefined,
      };

      console.log("Sending student data:", studentData);

      const result = await createStudent(studentData);

      if (result.success) {
        toast.success("Student created successfully");
        navigate(`/students/${result.data.id}`);
      } else {
        const errorMessage = (result as any).error || "Failed to create student";
        toast.error(errorMessage);
        console.error("Create student failed:", result);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An error occurred while creating student";
      toast.error(errorMessage);
      console.error("Create student error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Add a new parent
  const addParent = () => {
    append({
      fullName: "",
      relationship: "father",
      phone: "",
      email: "",
      occupation: "",
      isEmergencyContact: fields.length === 0, // Default first parent as emergency contact
    });
  };
  
  return (
    <MainLayout>
      <div className="space-y-6 max-w-4xl mx-auto">
        <div className="flex items-center gap-2">
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={() => navigate('/students')}
          >
            <ChevronLeft size={18} />
          </Button>
          <h1 className="text-2xl font-bold">Register New Student</h1>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Student Registration Form</CardTitle>
            <CardDescription>
              Enter student details to register a new student
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
                {/* Personal Information Section */}
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <User size={18} />
                    Personal Information
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="firstName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>First Name</FormLabel>
                          <FormControl>
                            <Input 
                              placeholder="First name" 
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            Student's legal first name
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="lastName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Last Name</FormLabel>
                          <FormControl>
                            <Input 
                              placeholder="Last name" 
                              {...field} 
                            />
                          </FormControl>
                          <FormDescription>
                            Student's legal last name
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Status</FormLabel>
                          <Select 
                            onValueChange={field.onChange} 
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="active">Active</SelectItem>
                              <SelectItem value="pending">Pending</SelectItem>
                              <SelectItem value="inactive">Inactive</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Current enrollment status
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
                
                {/* Contact Information Section */}
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <Phone size={18} />
                    Contact Information
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email Address</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                              <Input 
                                placeholder="<EMAIL>" 
                                {...field} 
                                className="pl-9"
                              />
                            </div>
                          </FormControl>
                          <FormDescription>
                            Primary contact email
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Phone Number</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <Phone className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                              <Input 
                                placeholder="+1234567890" 
                                {...field} 
                                className="pl-9"
                              />
                            </div>
                          </FormControl>
                          <FormDescription>
                            Primary contact phone (include country code)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="address"
                      render={({ field }) => (
                        <FormItem className="md:col-span-2">
                          <FormLabel>Address</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                              <Textarea 
                                placeholder="Enter full address" 
                                {...field} 
                                className="min-h-[80px] pl-9"
                              />
                            </div>
                          </FormControl>
                          <FormDescription>
                            Student's residential address
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
                
                {/* Parents Information Section */}
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                      <Users size={18} />
                      Parents Information
                    </h3>
                    <Button 
                      type="button" 
                      variant="outline" 
                      size="sm" 
                      onClick={addParent}
                    >
                      <Plus size={16} className="mr-2" />
                      Add Parent
                    </Button>
                  </div>
                  
                  {fields.map((field, index) => (
                    <div 
                      key={field.id} 
                      className="p-4 border rounded-md mb-6 bg-muted/20"
                    >
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-medium">Parent/Guardian {index + 1}</h4>
                        {fields.length > 1 && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => remove(index)}
                            className="text-destructive hover:text-destructive"
                          >
                            <Trash size={16} className="mr-2" />
                            Remove
                          </Button>
                        )}
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormField
                          control={form.control}
                          name={`parents.${index}.fullName`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Full Name</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="Parent full name" 
                                  {...field} 
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name={`parents.${index}.relationship`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Relationship</FormLabel>
                              <Select 
                                onValueChange={field.onChange} 
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select relationship" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="mother">Mother</SelectItem>
                                  <SelectItem value="father">Father</SelectItem>
                                  <SelectItem value="guardian">Guardian</SelectItem>
                                  <SelectItem value="other">Other</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name={`parents.${index}.phone`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Phone Number</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <Phone className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                  <Input 
                                    placeholder="+1234567890" 
                                    {...field} 
                                    className="pl-9"
                                  />
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name={`parents.${index}.email`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Email (Optional)</FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                  <Input 
                                    placeholder="<EMAIL>" 
                                    {...field} 
                                    className="pl-9"
                                  />
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name={`parents.${index}.occupation`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>Occupation (Optional)</FormLabel>
                              <FormControl>
                                <Input 
                                  placeholder="Occupation" 
                                  {...field} 
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        
                        <FormField
                          control={form.control}
                          name={`parents.${index}.isEmergencyContact`}
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-4">
                              <FormControl>
                                <input
                                  type="checkbox"
                                  checked={field.value}
                                  onChange={field.onChange}
                                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                                />
                              </FormControl>
                              <div className="space-y-1 leading-none">
                                <FormLabel>Emergency Contact</FormLabel>
                                <FormDescription>
                                  Is this person the primary emergency contact?
                                </FormDescription>
                              </div>
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  ))}
                </div>
                
                {/* Education Section */}
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <BookOpen size={18} />
                    Education Assignment
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="currentLevel"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Current Level</FormLabel>
                          <Select 
                            onValueChange={field.onChange} 
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select level" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="Beginner">Beginner</SelectItem>
                              <SelectItem value="Intermediate">Intermediate</SelectItem>
                              <SelectItem value="Advanced">Advanced</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Student's current education level
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={form.control}
                      name="currentClassId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Class Assignment (Optional)</FormLabel>
                          <Select 
                            onValueChange={field.onChange} 
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select class" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="no-class">No Class Assignment</SelectItem>
                              {currentLevel && suggestedClasses?.map(c => (
                                <SelectItem key={c.id} value={c.id}>
                                  {c.name}
                                </SelectItem>
                              ))}
                              {!currentLevel && (
                                <SelectItem value="select-level" disabled>
                                  Select a level first
                                </SelectItem>
                              )}
                              {currentLevel && isLoadingClasses && (
                                <SelectItem value="loading" disabled>
                                  Loading classes...
                                </SelectItem>
                              )}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Optional initial class assignment
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
                
                {/* Additional Notes Section */}
                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Additional Notes (Optional)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter any additional notes about this student..."
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Any special requirements or additional information
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="flex justify-end gap-3 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate('/students')}
                  >
                    Cancel
                  </Button>
                  <Button 
                    type="submit"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Registering..." : "Register Student"}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
